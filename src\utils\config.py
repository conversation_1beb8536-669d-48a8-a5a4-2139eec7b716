"""
配置管理器

负责加载和管理游戏配置参数。
"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            # 默认配置文件路径
            config_path = Path(__file__).parent.parent.parent / "config" / "simulation_config.yaml"
        
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_path}")
            self.config = self._get_default_config()
        except yaml.YAMLError as e:
            print(f"配置文件解析错误: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'world': {
                'width': 100,
                'height': 100,
                'boundary_type': 'wrap'
            },
            'energy': {
                'initial_energy_per_cell': 10,
                'energy_generation_rate': 0.1,
                'energy_decay_rate': 0.01,
                'max_energy_per_cell': 100
            },
            'units': {
                'core': {'build_cost': 50, 'upkeep_cost': 1, 'max_health': 100},
                'absorber': {'build_cost': 20, 'upkeep_cost': 2, 'absorption_rate': 5},
                'connector': {'build_cost': 10, 'upkeep_cost': 0.5},
                'mover': {'build_cost': 30, 'upkeep_cost': 3, 'move_cost': 5}
            },
            'organism': {
                'initial_energy': 100,
                'reproduction_threshold': 200,
                'mutation_rate': 0.05,
                'max_size': 50
            },
            'breakpoints': {
                'longevity_threshold': 1000,
                'population_threshold': 50,
                'migration_threshold': 500,
                'efficiency_threshold': 0.8
            },
            'visualization': {
                'window_width': 1200,
                'window_height': 800,
                'grid_cell_size': 8,
                'fps': 60,
                'show_energy': True,
                'show_connections': True
            },
            'performance': {
                'use_cython': False,
                'max_organisms': 1000,
                'simulation_threads': 1
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def save_config(self, path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            path: 保存路径，如果为None则使用原路径
        """
        save_path = Path(path) if path else self.config_path
        
        try:
            save_path.parent.mkdir(parents=True, exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"保存配置文件失败: {e}")


# 全局配置实例
config = ConfigManager()
