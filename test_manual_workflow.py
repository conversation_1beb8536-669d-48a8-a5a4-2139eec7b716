"""
手动工作流程测试

测试新的AI响应保存和手动编译工作流程。
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.base_unit import UnitType, create_unit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from ai_integration.breakpoint_detector import BreakpointDetector
from ai_integration.ai_integrator import AIIntegrator
from ai_integration.breakpoint_manager import BreakpointManager


def create_test_scenario():
    """创建测试场景"""
    print("=== 创建测试场景 ===")
    
    # 创建网格
    grid = Grid(50, 50, BoundaryType.WRAP)
    grid.distribute_energy(1000)
    
    # 创建一个会触发断点的生物体
    organism = Organism("manual_test")
    
    # 创建基本结构
    core = create_unit(UnitType.CORE, "core_1", (25, 25))
    absorber = create_unit(UnitType.ABSORBER, "absorber_1", (25, 26))
    mover = create_unit(UnitType.MOVER, "mover_1", (26, 25))
    
    # 添加到生物体
    organism.add_unit(core)
    organism.add_unit(absorber)
    organism.add_unit(mover)
    
    # 放置到网格
    grid.place_unit(core, 25, 25)
    grid.place_unit(absorber, 25, 26)
    grid.place_unit(mover, 26, 25)
    
    # 设置触发断点的状态
    organism.energy = 150.0
    organism.age = 1200.0  # 触发生存断点
    organism.total_distance_moved = 600.0  # 触发迁移断点
    organism.energy_absorbed_total = 1000.0
    organism.reproduction_count = 8
    
    print(f"创建测试生物体: {organism.organism_id}")
    print(f"  年龄: {organism.age} (应触发生存断点)")
    print(f"  移动距离: {organism.total_distance_moved} (应触发迁移断点)")
    print(f"  单位数量: {organism.unit_count}")
    
    return grid, [organism]


async def test_manual_workflow():
    """测试手动工作流程"""
    print("\n=== 测试手动工作流程 ===")
    
    # 创建测试场景
    grid, organisms = create_test_scenario()
    
    # 创建断点管理器
    manager = BreakpointManager()
    
    # 设置回调函数
    def on_breakpoint(event):
        print(f"🎯 断点触发: {event.description}")
    
    def on_ai_response(event, ai_result):
        if ai_result.get('status') == 'awaiting_compilation':
            print(f"💾 AI响应已保存: {ai_result['response_file']}")
            print(f"🛑 游戏已暂停，等待手动编译")
        else:
            print(f"🤖 AI响应: {ai_result}")
    
    manager.set_breakpoint_callback(on_breakpoint)
    manager.set_ai_response_callback(on_ai_response)
    
    # 检测断点
    print("\n🔍 检测断点...")
    breakpoint_triggered = manager.update(organisms, grid)
    
    if breakpoint_triggered:
        print(f"✅ 断点已触发，管理器状态: 暂停={manager.is_paused}")
        
        # 处理断点
        print("\n🤖 处理断点...")
        ai_results = await manager.process_pending_breakpoints(organisms, grid)
        
        print(f"\n📊 处理结果:")
        print(f"  AI结果数量: {len(ai_results)}")
        print(f"  管理器状态: 暂停={manager.is_paused}")
        
        # 显示AI响应文件
        for i, result in enumerate(ai_results, 1):
            if result.get('status') == 'awaiting_compilation':
                print(f"\n📁 AI响应 {i}:")
                print(f"  文件: {result['response_file']}")
                print(f"  断点: {result['breakpoint_info']['description']}")
                
                # 显示文件内容的前几行
                try:
                    with open(result['response_file'], 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"  文件内容预览 (前10行):")
                        for j, line in enumerate(lines[:10], 1):
                            print(f"    {j:2d}: {line.rstrip()}")
                        if len(lines) > 10:
                            print(f"    ... (还有 {len(lines) - 10} 行)")
                except Exception as e:
                    print(f"  ❌ 无法读取文件: {e}")
        
        # 模拟手动恢复
        print(f"\n🔄 模拟手动恢复...")
        print(f"  (在实际使用中，您需要先手动编译AI建议的新单位)")
        
        manager.resume_simulation()
        print(f"✅ 模拟已恢复，管理器状态: 暂停={manager.is_paused}")
        
        return ai_results
    else:
        print("❌ 没有触发断点")
        return []


def show_workflow_instructions():
    """显示工作流程说明"""
    print("\n" + "="*60)
    print("🔧 手动编译工作流程说明")
    print("="*60)
    print("""
当AI响应保存后，您需要手动执行以下步骤：

1. 📖 阅读AI响应文件
   - 文件位置: data/ai_response_*.txt
   - 包含AI的完整设计建议

2. 🔧 编译新单位类型
   a) 在 src/core/base_unit.py 中:
      - 添加新的 UnitType 枚举值
      - 创建新的单位类 (继承 BaseUnit)
      - 在 create_unit 函数中添加创建逻辑
   
   b) 在 config/simulation_config.yaml 中:
      - 添加新单位的配置参数

3. ▶️ 恢复模拟
   - 在游戏中按 'C' 键手动恢复
   - 或调用 manager.resume_simulation()

4. 🎮 观察演化
   - 新单位将自动加入突变池
   - 观察生物体如何使用新单位

这种工作流程确保了：
✅ AI建议得到人工审核
✅ 代码质量和一致性
✅ 游戏平衡性控制
✅ 完整的演化记录
""")
    print("="*60)


async def main():
    """主测试函数"""
    print("手动工作流程测试")
    print("=" * 50)
    
    try:
        # 运行测试
        ai_results = await test_manual_workflow()
        
        # 显示工作流程说明
        show_workflow_instructions()
        
        # 总结
        print(f"\n📋 测试总结:")
        print(f"  AI响应数量: {len(ai_results)}")
        
        if ai_results:
            print(f"  ✅ 工作流程测试成功!")
            print(f"  📁 生成的文件:")
            for result in ai_results:
                if result.get('response_file'):
                    print(f"    - {result['response_file']}")
        else:
            print(f"  ❌ 没有生成AI响应")
        
        print(f"\n🎯 下一步: 手动编译AI建议的新单位，然后恢复模拟")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
