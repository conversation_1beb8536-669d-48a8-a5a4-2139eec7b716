"""
断点检测系统

监控模拟过程中的各种里程碑条件，当达成特定成就时触发断点。
"""

from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.organism import Organism
from core.grid import Grid
from utils.config import config


class BreakpointType(Enum):
    """断点类型"""
    LONGEVITY = "longevity"          # 生存里程碑
    POPULATION = "population"        # 种群里程碑
    MIGRATION = "migration"          # 迁移里程碑
    EFFICIENCY = "efficiency"        # 效率里程碑
    REPRODUCTION = "reproduction"    # 繁殖里程碑
    STRUCTURE = "structure"          # 结构里程碑


@dataclass
class BreakpointEvent:
    """断点事件"""
    breakpoint_type: BreakpointType
    organism_id: str
    trigger_value: float
    threshold: float
    timestamp: float
    description: str
    context_data: Dict[str, Any]


class BreakpointDetector:
    """
    断点检测器
    
    监控模拟过程中的各种里程碑条件，检测到达成条件时触发断点事件。
    """
    
    def __init__(self):
        # 从配置加载阈值
        self.thresholds = {
            BreakpointType.LONGEVITY: config.get('breakpoints.longevity_threshold', 1000),
            BreakpointType.POPULATION: config.get('breakpoints.population_threshold', 50),
            BreakpointType.MIGRATION: config.get('breakpoints.migration_threshold', 500),
            BreakpointType.EFFICIENCY: config.get('breakpoints.efficiency_threshold', 0.8),
            BreakpointType.REPRODUCTION: config.get('breakpoints.reproduction_threshold', 10),
            BreakpointType.STRUCTURE: config.get('breakpoints.structure_threshold', 20)
        }
        
        # 历史记录
        self.triggered_breakpoints: List[BreakpointEvent] = []
        self.organism_records: Dict[str, Dict[str, Any]] = {}
        
        # 统计数据
        self.global_stats = {
            'max_longevity': 0.0,
            'max_migration': 0.0,
            'max_efficiency': 0.0,
            'max_reproduction': 0,
            'max_structure_size': 0,
            'total_organisms_created': 0
        }
        
        print("断点检测器初始化完成")
        print(f"阈值设置: {self.thresholds}")
    
    def update_organism_records(self, organisms: List[Organism]) -> None:
        """更新生物体记录"""
        current_time = time.time()
        
        for organism in organisms:
            if not organism.is_alive:
                continue
            
            org_id = organism.organism_id
            
            # 初始化记录
            if org_id not in self.organism_records:
                self.organism_records[org_id] = {
                    'birth_time': current_time,
                    'max_energy': organism.energy,
                    'total_energy_absorbed': 0.0,
                    'total_distance_moved': 0.0,
                    'reproduction_count': 0,
                    'max_structure_size': organism.structure_size,
                    'last_update': current_time
                }
            
            record = self.organism_records[org_id]
            
            # 更新记录
            record['max_energy'] = max(record['max_energy'], organism.energy)
            record['total_energy_absorbed'] = organism.energy_absorbed_total
            record['total_distance_moved'] = organism.total_distance_moved
            record['reproduction_count'] = organism.reproduction_count
            record['max_structure_size'] = max(record['max_structure_size'], organism.structure_size)
            record['last_update'] = current_time
            
            # 更新全局统计
            self.global_stats['max_longevity'] = max(self.global_stats['max_longevity'], organism.age)
            self.global_stats['max_migration'] = max(self.global_stats['max_migration'], organism.total_distance_moved)
            self.global_stats['max_reproduction'] = max(self.global_stats['max_reproduction'], organism.reproduction_count)
            self.global_stats['max_structure_size'] = max(self.global_stats['max_structure_size'], organism.structure_size)
    
    def check_breakpoints(self, organisms: List[Organism], grid: Grid) -> List[BreakpointEvent]:
        """
        检查是否触发断点
        
        Args:
            organisms: 当前存活的生物体列表
            grid: 游戏网格
            
        Returns:
            触发的断点事件列表
        """
        self.update_organism_records(organisms)
        triggered_events = []
        
        for organism in organisms:
            if not organism.is_alive:
                continue
            
            # 检查各种断点条件
            events = []
            events.extend(self._check_longevity_breakpoint(organism))
            events.extend(self._check_migration_breakpoint(organism))
            events.extend(self._check_efficiency_breakpoint(organism))
            events.extend(self._check_reproduction_breakpoint(organism))
            events.extend(self._check_structure_breakpoint(organism))
            
            triggered_events.extend(events)
        
        # 检查种群级别的断点
        triggered_events.extend(self._check_population_breakpoint(organisms))
        
        # 记录触发的断点
        for event in triggered_events:
            self.triggered_breakpoints.append(event)
            print(f"🎯 断点触发: {event.breakpoint_type.value} - {event.description}")
        
        return triggered_events
    
    def _check_longevity_breakpoint(self, organism: Organism) -> List[BreakpointEvent]:
        """检查生存里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.LONGEVITY]
        
        if organism.age >= threshold:
            # 检查是否已经触发过
            if not self._has_triggered(organism.organism_id, BreakpointType.LONGEVITY):
                event = BreakpointEvent(
                    breakpoint_type=BreakpointType.LONGEVITY,
                    organism_id=organism.organism_id,
                    trigger_value=organism.age,
                    threshold=threshold,
                    timestamp=time.time(),
                    description=f"生物体 {organism.organism_id[:8]} 存活了 {organism.age:.1f} 回合",
                    context_data={
                        'organism_stats': organism.get_statistics(),
                        'structure': self._analyze_structure(organism),
                        'survival_strategy': self._analyze_survival_strategy(organism)
                    }
                )
                events.append(event)
        
        return events
    
    def _check_migration_breakpoint(self, organism: Organism) -> List[BreakpointEvent]:
        """检查迁移里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.MIGRATION]
        
        if organism.total_distance_moved >= threshold:
            if not self._has_triggered(organism.organism_id, BreakpointType.MIGRATION):
                event = BreakpointEvent(
                    breakpoint_type=BreakpointType.MIGRATION,
                    organism_id=organism.organism_id,
                    trigger_value=organism.total_distance_moved,
                    threshold=threshold,
                    timestamp=time.time(),
                    description=f"生物体 {organism.organism_id[:8]} 移动了 {organism.total_distance_moved:.1f} 格",
                    context_data={
                        'organism_stats': organism.get_statistics(),
                        'movement_pattern': self._analyze_movement_pattern(organism),
                        'exploration_efficiency': organism.total_distance_moved / max(organism.age, 1)
                    }
                )
                events.append(event)
        
        return events
    
    def _check_efficiency_breakpoint(self, organism: Organism) -> List[BreakpointEvent]:
        """检查效率里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.EFFICIENCY]
        
        # 计算能量效率（吸收/维持比率）
        upkeep_cost = organism.calculate_upkeep_cost()
        if upkeep_cost > 0:
            efficiency = organism.energy_absorbed_total / (upkeep_cost * organism.age)
            
            if efficiency >= threshold:
                if not self._has_triggered(organism.organism_id, BreakpointType.EFFICIENCY):
                    event = BreakpointEvent(
                        breakpoint_type=BreakpointType.EFFICIENCY,
                        organism_id=organism.organism_id,
                        trigger_value=efficiency,
                        threshold=threshold,
                        timestamp=time.time(),
                        description=f"生物体 {organism.organism_id[:8]} 达到 {efficiency:.2f} 能量效率",
                        context_data={
                            'organism_stats': organism.get_statistics(),
                            'efficiency_analysis': {
                                'total_absorbed': organism.energy_absorbed_total,
                                'total_upkeep': upkeep_cost * organism.age,
                                'efficiency_ratio': efficiency
                            }
                        }
                    )
                    events.append(event)
        
        return events
    
    def _check_reproduction_breakpoint(self, organism: Organism) -> List[BreakpointEvent]:
        """检查繁殖里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.REPRODUCTION]
        
        if organism.reproduction_count >= threshold:
            if not self._has_triggered(organism.organism_id, BreakpointType.REPRODUCTION):
                event = BreakpointEvent(
                    breakpoint_type=BreakpointType.REPRODUCTION,
                    organism_id=organism.organism_id,
                    trigger_value=organism.reproduction_count,
                    threshold=threshold,
                    timestamp=time.time(),
                    description=f"生物体 {organism.organism_id[:8]} 繁殖了 {organism.reproduction_count} 次",
                    context_data={
                        'organism_stats': organism.get_statistics(),
                        'reproductive_success': self._analyze_reproductive_success(organism)
                    }
                )
                events.append(event)
        
        return events
    
    def _check_structure_breakpoint(self, organism: Organism) -> List[BreakpointEvent]:
        """检查结构里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.STRUCTURE]
        
        if organism.structure_size >= threshold:
            if not self._has_triggered(organism.organism_id, BreakpointType.STRUCTURE):
                event = BreakpointEvent(
                    breakpoint_type=BreakpointType.STRUCTURE,
                    organism_id=organism.organism_id,
                    trigger_value=organism.structure_size,
                    threshold=threshold,
                    timestamp=time.time(),
                    description=f"生物体 {organism.organism_id[:8]} 发展出 {organism.structure_size} 个单位的复杂结构",
                    context_data={
                        'organism_stats': organism.get_statistics(),
                        'structure_analysis': self._analyze_structure(organism)
                    }
                )
                events.append(event)
        
        return events
    
    def _check_population_breakpoint(self, organisms: List[Organism]) -> List[BreakpointEvent]:
        """检查种群里程碑"""
        events = []
        threshold = self.thresholds[BreakpointType.POPULATION]
        
        if len(organisms) >= threshold:
            # 检查是否已经触发过种群断点
            if not any(event.breakpoint_type == BreakpointType.POPULATION for event in self.triggered_breakpoints):
                event = BreakpointEvent(
                    breakpoint_type=BreakpointType.POPULATION,
                    organism_id="population",
                    trigger_value=len(organisms),
                    threshold=threshold,
                    timestamp=time.time(),
                    description=f"种群规模达到 {len(organisms)} 个生物体",
                    context_data={
                        'population_analysis': self._analyze_population(organisms),
                        'diversity_metrics': self._calculate_diversity(organisms)
                    }
                )
                events.append(event)
        
        return events
    
    def _has_triggered(self, organism_id: str, breakpoint_type: BreakpointType) -> bool:
        """检查是否已经触发过指定类型的断点"""
        return any(
            event.organism_id == organism_id and event.breakpoint_type == breakpoint_type
            for event in self.triggered_breakpoints
        )
    
    def _analyze_structure(self, organism: Organism) -> Dict[str, Any]:
        """分析生物体结构"""
        stats = organism.get_statistics()
        return {
            'unit_composition': stats['unit_types'],
            'total_units': stats['unit_count'],
            'structure_efficiency': stats['unit_count'] / max(stats['energy'], 1),
            'core_position': organism.get_core_position()
        }
    
    def _analyze_survival_strategy(self, organism: Organism) -> Dict[str, Any]:
        """分析生存策略"""
        stats = organism.get_statistics()
        return {
            'energy_management': {
                'current_energy': stats['energy'],
                'total_absorbed': stats['energy_absorbed_total'],
                'absorption_rate': stats['energy_absorbed_total'] / max(stats['age'], 1)
            },
            'mobility': {
                'total_distance': stats['total_distance_moved'],
                'movement_rate': stats['total_distance_moved'] / max(stats['age'], 1)
            },
            'reproduction': {
                'reproduction_count': stats['reproduction_count'],
                'reproduction_rate': stats['reproduction_count'] / max(stats['age'], 1)
            }
        }
    
    def _analyze_movement_pattern(self, organism: Organism) -> Dict[str, Any]:
        """分析移动模式"""
        from core.base_unit import UnitType
        return {
            'total_distance': organism.total_distance_moved,
            'movement_efficiency': organism.total_distance_moved / max(organism.age, 1),
            'has_mover_units': len(organism.get_units_by_type(UnitType.MOVER)) > 0
        }
    
    def _analyze_reproductive_success(self, organism: Organism) -> Dict[str, Any]:
        """分析繁殖成功率"""
        return {
            'reproduction_count': organism.reproduction_count,
            'reproduction_rate': organism.reproduction_count / max(organism.age, 1),
            'energy_investment': organism.reproduction_count * organism.reproduction_threshold
        }
    
    def _analyze_population(self, organisms: List[Organism]) -> Dict[str, Any]:
        """分析种群特征"""
        if not organisms:
            return {}
        
        total_energy = sum(org.energy for org in organisms)
        total_age = sum(org.age for org in organisms)
        total_units = sum(org.unit_count for org in organisms)
        
        return {
            'population_size': len(organisms),
            'average_energy': total_energy / len(organisms),
            'average_age': total_age / len(organisms),
            'average_structure_size': total_units / len(organisms),
            'total_reproductions': sum(org.reproduction_count for org in organisms)
        }
    
    def _calculate_diversity(self, organisms: List[Organism]) -> Dict[str, Any]:
        """计算种群多样性"""
        if not organisms:
            return {}
        
        # 结构多样性
        structure_types = {}
        for organism in organisms:
            structure_key = tuple(sorted(organism.get_statistics()['unit_types'].items()))
            structure_types[structure_key] = structure_types.get(structure_key, 0) + 1
        
        return {
            'unique_structures': len(structure_types),
            'structure_distribution': dict(structure_types),
            'diversity_index': len(structure_types) / len(organisms)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取断点检测器统计信息"""
        return {
            'total_breakpoints_triggered': len(self.triggered_breakpoints),
            'breakpoints_by_type': {
                bp_type.value: len([e for e in self.triggered_breakpoints if e.breakpoint_type == bp_type])
                for bp_type in BreakpointType
            },
            'global_stats': self.global_stats,
            'thresholds': {bp_type.value: threshold for bp_type, threshold in self.thresholds.items()}
        }
