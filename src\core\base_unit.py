"""
基本单位系统 - 所有生命单位的基类和具体实现

这个模块定义了生命游戏中所有基本单位的抽象基类和具体实现。
每个单位都有特定的功能、能量消耗和行为逻辑。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional, List
from enum import Enum
import numpy as np


class UnitType(Enum):
    """单位类型枚举"""
    CORE = "core"
    ABSORBER = "absorber"
    CONNECTOR = "connector"
    MOVER = "mover"


class Direction(Enum):
    """方向枚举"""
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)
    UP_LEFT = (-1, -1)
    UP_RIGHT = (1, -1)
    DOWN_LEFT = (-1, 1)
    DOWN_RIGHT = (1, 1)


class BaseUnit(ABC):
    """
    基本单位抽象基类
    
    所有生命单位的基础类，定义了单位的基本属性和行为接口。
    """
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        self.unit_id = unit_id
        self.position = position
        self.health = self.max_health
        self.age = 0
        self.organism_id: Optional[str] = None
        
    @property
    @abstractmethod
    def unit_type(self) -> UnitType:
        """单位类型"""
        pass
    
    @property
    @abstractmethod
    def build_cost(self) -> float:
        """建造成本"""
        pass
    
    @property
    @abstractmethod
    def upkeep_cost(self) -> float:
        """维持成本（每回合）"""
        pass
    
    @property
    @abstractmethod
    def max_health(self) -> float:
        """最大生命值"""
        pass
    
    @abstractmethod
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单位的核心功能
        
        Args:
            context: 环境上下文信息
            
        Returns:
            执行结果字典
        """
        pass
    
    def update(self, delta_time: float) -> None:
        """更新单位状态"""
        self.age += delta_time
    
    def take_damage(self, damage: float) -> bool:
        """
        受到伤害
        
        Args:
            damage: 伤害值
            
        Returns:
            是否死亡
        """
        self.health -= damage
        return self.health <= 0
    
    def heal(self, amount: float) -> None:
        """治疗"""
        self.health = min(self.health + amount, self.max_health)
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'unit_id': self.unit_id,
            'unit_type': self.unit_type.value,
            'position': self.position,
            'health': self.health,
            'age': self.age,
            'organism_id': self.organism_id
        }


class CoreUnit(BaseUnit):
    """
    核心单位 - 生物体的中心
    
    每个生物体必须有且只有一个核心单位。
    核心单位负责维持生物体的结构完整性和能量存储。
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.CORE
    
    @property
    def build_cost(self) -> float:
        return 50.0
    
    @property
    def upkeep_cost(self) -> float:
        return 1.0
    
    @property
    def max_health(self) -> float:
        return 100.0
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        super().__init__(unit_id, position)
        self.energy_storage = 0.0
        self.max_energy_storage = 100.0
    
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """核心单位的功能：能量存储和结构维持"""
        return {
            'action': 'maintain_structure',
            'energy_stored': self.energy_storage,
            'storage_capacity': self.max_energy_storage
        }
    
    def store_energy(self, amount: float) -> float:
        """存储能量，返回实际存储的量"""
        available_space = self.max_energy_storage - self.energy_storage
        stored = min(amount, available_space)
        self.energy_storage += stored
        return stored
    
    def consume_energy(self, amount: float) -> float:
        """消耗能量，返回实际消耗的量"""
        consumed = min(amount, self.energy_storage)
        self.energy_storage -= consumed
        return consumed


class AbsorberUnit(BaseUnit):
    """
    吸收单位 - 从环境中吸收能量
    
    吸收单位可以从所在位置或相邻位置吸收环境能量。
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.ABSORBER
    
    @property
    def build_cost(self) -> float:
        return 20.0
    
    @property
    def upkeep_cost(self) -> float:
        return 2.0
    
    @property
    def max_health(self) -> float:
        return 50.0
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        super().__init__(unit_id, position)
        self.absorption_rate = 5.0
        self.absorption_range = 1  # 吸收范围（格子数）
    
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """吸收单位的功能：从环境吸收能量"""
        grid = context.get('grid')
        if not grid:
            return {'action': 'absorb', 'energy_absorbed': 0.0}
        
        total_absorbed = 0.0
        x, y = self.position
        
        # 在吸收范围内寻找能量
        for dx in range(-self.absorption_range, self.absorption_range + 1):
            for dy in range(-self.absorption_range, self.absorption_range + 1):
                target_x, target_y = x + dx, y + dy
                if grid.is_valid_position(target_x, target_y):
                    cell = grid.get_cell(target_x, target_y)
                    if cell and cell.energy > 0:
                        absorbed = min(self.absorption_rate, cell.energy)
                        cell.energy -= absorbed
                        total_absorbed += absorbed
        
        return {
            'action': 'absorb',
            'energy_absorbed': total_absorbed
        }


class ConnectorUnit(BaseUnit):
    """
    连接单位 - 连接其他单位形成结构
    
    连接单位本身没有特殊功能，主要用于扩展生物体的结构。
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.CONNECTOR
    
    @property
    def build_cost(self) -> float:
        return 10.0
    
    @property
    def upkeep_cost(self) -> float:
        return 0.5
    
    @property
    def max_health(self) -> float:
        return 30.0
    
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """连接单位的功能：维持连接"""
        return {
            'action': 'connect',
            'connections': self.get_adjacent_units(context)
        }
    
    def get_adjacent_units(self, context: Dict[str, Any]) -> List[str]:
        """获取相邻的单位ID列表"""
        grid = context.get('grid')
        if not grid:
            return []
        
        adjacent_units = []
        x, y = self.position
        
        for direction in Direction:
            dx, dy = direction.value
            target_x, target_y = x + dx, y + dy
            if grid.is_valid_position(target_x, target_y):
                cell = grid.get_cell(target_x, target_y)
                if cell and cell.unit and cell.unit.organism_id == self.organism_id:
                    adjacent_units.append(cell.unit.unit_id)
        
        return adjacent_units


class MoverUnit(BaseUnit):
    """
    推进单位 - 移动生物体
    
    推进单位可以消耗能量来移动整个生物体。
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.MOVER
    
    @property
    def build_cost(self) -> float:
        return 30.0
    
    @property
    def upkeep_cost(self) -> float:
        return 3.0
    
    @property
    def max_health(self) -> float:
        return 40.0
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        super().__init__(unit_id, position)
        self.move_cost = 5.0
        self.push_direction = Direction.RIGHT  # 默认推进方向
    
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """推进单位的功能：移动生物体"""
        organism = context.get('organism')
        if not organism:
            return {'action': 'move', 'success': False, 'reason': 'no_organism'}
        
        # 检查是否有足够的能量
        if organism.energy < self.move_cost:
            return {'action': 'move', 'success': False, 'reason': 'insufficient_energy'}
        
        # 计算移动方向（相对于核心位置）
        core_position = organism.get_core_position()
        if not core_position:
            return {'action': 'move', 'success': False, 'reason': 'no_core'}
        
        # 计算推进方向
        dx, dy = self.push_direction.value
        target_direction = (dx, dy)
        
        return {
            'action': 'move',
            'direction': target_direction,
            'energy_cost': self.move_cost,
            'success': True
        }
    
    def set_push_direction(self, direction: Direction) -> None:
        """设置推进方向"""
        self.push_direction = direction


# 单位工厂函数
def create_unit(unit_type: UnitType, unit_id: str, position: Tuple[int, int]) -> BaseUnit:
    """
    创建指定类型的单位
    
    Args:
        unit_type: 单位类型
        unit_id: 单位ID
        position: 位置
        
    Returns:
        创建的单位实例
    """
    unit_classes = {
        UnitType.CORE: CoreUnit,
        UnitType.ABSORBER: AbsorberUnit,
        UnitType.CONNECTOR: ConnectorUnit,
        UnitType.MOVER: MoverUnit
    }
    
    unit_class = unit_classes.get(unit_type)
    if not unit_class:
        raise ValueError(f"Unknown unit type: {unit_type}")
    
    return unit_class(unit_id, position)
