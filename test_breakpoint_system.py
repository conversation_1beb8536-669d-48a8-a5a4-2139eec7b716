"""
断点系统测试

测试断点检测、AI集成和新单位创建的完整流程。
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.base_unit import UnitType, create_unit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from ai_integration.breakpoint_detector import BreakpointDetector, BreakpointType
from ai_integration.context_reporter import ContextReporter
from ai_integration.ai_integrator import AIIntegrator
from ai_integration.breakpoint_manager import BreakpointManager
from utils.config import config


def create_test_scenario():
    """创建测试场景"""
    print("=== 创建测试场景 ===")
    
    # 创建网格
    grid = Grid(50, 50, BoundaryType.WRAP)
    grid.distribute_energy(2000)
    
    # 创建一个长寿的生物体
    organism = Organism("longevity_test")
    
    # 创建复杂结构
    core = create_unit(UnitType.CORE, "core_1", (25, 25))
    absorber1 = create_unit(UnitType.ABSORBER, "absorber_1", (25, 26))
    absorber2 = create_unit(UnitType.ABSORBER, "absorber_2", (24, 25))
    mover = create_unit(UnitType.MOVER, "mover_1", (26, 25))
    connector = create_unit(UnitType.CONNECTOR, "connector_1", (25, 24))
    
    # 添加到生物体
    organism.add_unit(core)
    organism.add_unit(absorber1)
    organism.add_unit(absorber2)
    organism.add_unit(mover)
    organism.add_unit(connector)
    
    # 放置到网格
    grid.place_unit(core, 25, 25)
    grid.place_unit(absorber1, 25, 26)
    grid.place_unit(absorber2, 24, 25)
    grid.place_unit(mover, 26, 25)
    grid.place_unit(connector, 25, 24)
    
    # 给生物体大量能量和历史数据
    organism.energy = 500.0
    organism.age = 1200.0  # 超过生存阈值
    organism.total_distance_moved = 600.0  # 超过迁移阈值
    organism.energy_absorbed_total = 2000.0
    organism.reproduction_count = 12  # 超过繁殖阈值
    
    print(f"创建测试生物体: {organism.organism_id}")
    print(f"  年龄: {organism.age}")
    print(f"  移动距离: {organism.total_distance_moved}")
    print(f"  繁殖次数: {organism.reproduction_count}")
    print(f"  单位数量: {organism.unit_count}")
    
    return grid, [organism]


def test_breakpoint_detection():
    """测试断点检测"""
    print("\n=== 测试断点检测 ===")
    
    grid, organisms = create_test_scenario()
    detector = BreakpointDetector()
    
    # 检测断点
    events = detector.check_breakpoints(organisms, grid)
    
    print(f"检测到 {len(events)} 个断点:")
    for event in events:
        print(f"  - {event.breakpoint_type.value}: {event.description}")
    
    # 获取统计信息
    stats = detector.get_statistics()
    print(f"\n检测器统计:")
    print(f"  触发的断点总数: {stats['total_breakpoints_triggered']}")
    print(f"  各类型断点: {stats['breakpoints_by_type']}")
    
    return events


def test_context_reporting():
    """测试上下文报告生成"""
    print("\n=== 测试上下文报告生成 ===")
    
    grid, organisms = create_test_scenario()
    detector = BreakpointDetector()
    reporter = ContextReporter()
    
    # 检测断点
    events = detector.check_breakpoints(organisms, grid)
    
    if events:
        # 生成第一个断点的报告
        event = events[0]
        report = reporter.generate_breakpoint_report(event, organisms, grid)
        
        print(f"生成报告: {report['metadata']['report_id']}")
        print(f"断点类型: {report['metadata']['breakpoint_type']}")
        print(f"触发生物体: {report['metadata']['trigger_organism']}")
        
        # 显示AI提示词的一部分
        prompt = report['ai_prompt']
        print(f"\nAI提示词预览 (前200字符):")
        print(prompt[:200] + "...")
        
        # 保存报告
        filename = reporter.save_report(report)
        print(f"报告已保存: {filename}")
        
        return report
    else:
        print("没有检测到断点")
        return None


async def test_ai_integration():
    """测试AI集成"""
    print("\n=== 测试AI集成 ===")
    
    grid, organisms = create_test_scenario()
    detector = BreakpointDetector()
    ai_integrator = AIIntegrator()
    
    # 检测断点
    events = detector.check_breakpoints(organisms, grid)
    
    if events:
        # 处理第一个断点
        event = events[0]
        print(f"处理断点: {event.description}")
        
        unit_design = await ai_integrator.process_breakpoint(event, organisms, grid)
        
        if unit_design:
            print(f"✅ AI成功设计新单位:")
            print(f"  名称: {unit_design['name']}")
            print(f"  功能: {unit_design['function']}")
            print(f"  建造成本: {unit_design['build_cost']}")
            print(f"  维持成本: {unit_design['upkeep_cost']}")
            
            # 生成代码
            code = ai_integrator.get_unit_design_code(unit_design)
            print(f"\n生成的代码预览 (前300字符):")
            print(code[:300] + "...")
            
            return unit_design
        else:
            print("❌ AI集成失败")
            return None
    else:
        print("没有检测到断点")
        return None


async def test_breakpoint_manager():
    """测试断点管理器"""
    print("\n=== 测试断点管理器 ===")
    
    grid, organisms = create_test_scenario()
    manager = BreakpointManager()
    
    # 设置回调函数
    def on_breakpoint(event):
        print(f"🎯 断点回调: {event.description}")
    
    def on_ai_response(event, unit_design):
        print(f"🤖 AI响应回调: 为 {event.breakpoint_type.value} 设计了 {unit_design['name']}")
    
    def on_new_unit(unit_design):
        print(f"🆕 新单位回调: {unit_design['name']} 已创建")
    
    manager.set_breakpoint_callback(on_breakpoint)
    manager.set_ai_response_callback(on_ai_response)
    manager.set_new_unit_callback(on_new_unit)
    
    # 更新检测
    breakpoint_triggered = manager.update(organisms, grid)
    print(f"断点触发: {breakpoint_triggered}")
    
    if breakpoint_triggered:
        # 处理断点
        new_units = await manager.process_pending_breakpoints(organisms, grid)
        print(f"生成了 {len(new_units)} 个新单位设计")
        
        # 恢复模拟
        manager.resume_simulation()
        
        # 获取状态
        status = manager.get_current_status()
        print(f"\n管理器状态:")
        print(f"  是否暂停: {status['is_paused']}")
        print(f"  待处理断点: {status['pending_breakpoints']}")
        print(f"  已处理断点: {status['processed_breakpoints']}")
        
        # 导出会话数据
        export_file = manager.export_session_data()
        print(f"会话数据已导出: {export_file}")
        
        return new_units
    else:
        print("没有触发断点")
        return []


def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    import time
    
    # 创建大规模测试
    grid = Grid(100, 100, BoundaryType.WRAP)
    grid.distribute_energy(5000)
    
    organisms = []
    for i in range(50):
        organism = Organism(f"perf_test_{i}")
        
        # 创建基本结构
        core = create_unit(UnitType.CORE, f"core_{i}", (i, i))
        absorber = create_unit(UnitType.ABSORBER, f"absorber_{i}", (i+1, i))
        
        organism.add_unit(core)
        organism.add_unit(absorber)
        organism.energy = 100.0
        organism.age = 500.0  # 一些有年龄但不触发断点
        
        grid.place_unit(core, i, i)
        grid.place_unit(absorber, i+1, i)
        organisms.append(organism)
    
    # 创建一个会触发断点的生物体
    special_organism = organisms[0]
    special_organism.age = 1500.0  # 触发生存断点
    
    detector = BreakpointDetector()
    
    # 性能测试
    start_time = time.time()
    
    for _ in range(100):
        events = detector.check_breakpoints(organisms, grid)
    
    end_time = time.time()
    
    print(f"性能测试结果:")
    print(f"  100次检测耗时: {end_time - start_time:.3f} 秒")
    print(f"  平均每次检测: {(end_time - start_time) * 10:.3f} 毫秒")
    print(f"  检测到断点: {len(events) if 'events' in locals() else 0} 个")


async def main():
    """主测试函数"""
    print("元演化生命游戏 - 断点系统测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        print("开始测试断点系统...")
        
        # 1. 断点检测测试
        events = test_breakpoint_detection()
        
        # 2. 上下文报告测试
        report = test_context_reporting()
        
        # 3. AI集成测试
        unit_design = await test_ai_integration()
        
        # 4. 断点管理器测试
        new_units = await test_breakpoint_manager()
        
        # 5. 性能测试
        test_performance()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！断点系统运行正常。")
        
        # 总结
        print(f"\n测试总结:")
        print(f"  检测到断点: {len(events) if events else 0} 个")
        print(f"  生成报告: {'是' if report else '否'}")
        print(f"  AI设计单位: {'是' if unit_design else '否'}")
        print(f"  管理器处理: {len(new_units) if new_units else 0} 个新单位")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
