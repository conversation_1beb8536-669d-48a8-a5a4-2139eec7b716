"""
核心系统测试

测试基本单位、网格和生物体系统的基本功能。
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.base_unit import UnitType, create_unit, CoreUnit, AbsorberUnit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from utils.config import config


def test_unit_creation():
    """测试单位创建"""
    print("=== 测试单位创建 ===")
    
    # 创建核心单位
    core = create_unit(UnitType.CORE, "core_1", (5, 5))
    print(f"创建核心单位: {core.unit_type.value} at {core.position}")
    
    # 创建吸收单位
    absorber = create_unit(UnitType.ABSORBER, "absorber_1", (5, 6))
    print(f"创建吸收单位: {absorber.unit_type.value} at {absorber.position}")
    
    # 测试单位属性
    print(f"核心单位建造成本: {core.build_cost}")
    print(f"吸收单位维持成本: {absorber.upkeep_cost}")
    
    return core, absorber


def test_grid_system():
    """测试网格系统"""
    print("\n=== 测试网格系统 ===")
    
    # 创建网格
    grid = Grid(20, 20, BoundaryType.WRAP)
    print(f"创建网格: {grid.width}x{grid.height}")
    
    # 创建单位并放置到网格
    core = create_unit(UnitType.CORE, "core_1", (10, 10))
    success = grid.place_unit(core, 10, 10)
    print(f"放置核心单位: {'成功' if success else '失败'}")
    
    # 测试网格查询
    cell = grid.get_cell(10, 10)
    print(f"网格(10,10)有单位: {not cell.is_empty}")
    
    # 添加能量到网格
    grid.distribute_energy(1000.0)
    energy_map = grid.get_energy_map()
    print(f"网格总能量: {energy_map.sum():.2f}")
    
    return grid


def test_organism_system():
    """测试生物体系统"""
    print("\n=== 测试生物体系统 ===")
    
    # 创建生物体
    organism = Organism("test_organism")
    print(f"创建生物体: {organism.organism_id}")
    
    # 创建并添加单位
    core = create_unit(UnitType.CORE, "core_1", (10, 10))
    absorber = create_unit(UnitType.ABSORBER, "absorber_1", (10, 11))
    
    organism.add_unit(core)
    organism.add_unit(absorber)
    
    print(f"生物体单位数量: {organism.unit_count}")
    print(f"生物体是否存活: {organism.is_alive}")
    print(f"核心位置: {organism.get_core_position()}")
    
    # 给生物体一些能量
    organism.energy = 100.0
    print(f"生物体能量: {organism.energy}")
    
    return organism


def test_simulation_step():
    """测试模拟步骤"""
    print("\n=== 测试模拟步骤 ===")
    
    # 创建网格
    grid = Grid(20, 20, BoundaryType.WRAP)
    grid.distribute_energy(500.0)
    
    # 创建生物体
    organism = Organism("test_organism")
    
    # 创建单位
    core = create_unit(UnitType.CORE, "core_1", (10, 10))
    absorber = create_unit(UnitType.ABSORBER, "absorber_1", (10, 11))
    
    # 添加到生物体
    organism.add_unit(core)
    organism.add_unit(absorber)
    organism.energy = 50.0
    
    # 放置到网格
    grid.place_unit(core, 10, 10)
    grid.place_unit(absorber, 10, 11)
    
    print(f"模拟前 - 生物体能量: {organism.energy:.2f}")
    print(f"模拟前 - 网格(10,10)能量: {grid.get_cell(10, 10).energy:.2f}")
    
    # 执行一个模拟步骤
    organism.update(1.0, grid)
    
    print(f"模拟后 - 生物体能量: {organism.energy:.2f}")
    print(f"模拟后 - 网格(10,10)能量: {grid.get_cell(10, 10).energy:.2f}")
    
    # 获取统计信息
    stats = organism.get_statistics()
    print(f"生物体统计: {stats}")


def main():
    """主测试函数"""
    print("元演化生命游戏 - 核心系统测试")
    print("=" * 50)
    
    try:
        # 测试配置系统
        print(f"配置加载成功，世界大小: {config.get('world.width')}x{config.get('world.height')}")
        
        # 运行各项测试
        test_unit_creation()
        test_grid_system()
        test_organism_system()
        test_simulation_step()
        
        print("\n" + "=" * 50)
        print("所有测试完成！核心系统运行正常。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
