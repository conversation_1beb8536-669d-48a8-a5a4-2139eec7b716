"""
上下文报告生成器

为AI分析生成详细的环境和生物体状态报告。
"""

from typing import Dict, List, Any, Optional
import json
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from ai_integration.breakpoint_detector import BreakpointEvent, BreakpointType
from core.organism import Organism
from core.grid import Grid
from core.base_unit import UnitType


class ContextReporter:
    """
    上下文报告生成器
    
    分析断点事件的环境背景，生成供AI分析的详细报告。
    """
    
    def __init__(self):
        self.report_history: List[Dict[str, Any]] = []
        print("上下文报告生成器初始化完成")
    
    def generate_breakpoint_report(
        self, 
        event: BreakpointEvent, 
        organisms: List[Organism], 
        grid: Grid
    ) -> Dict[str, Any]:
        """
        生成断点事件的详细报告
        
        Args:
            event: 触发的断点事件
            organisms: 当前所有生物体
            grid: 游戏网格
            
        Returns:
            详细的上下文报告
        """
        print(f"生成断点报告: {event.breakpoint_type.value}")
        
        # 找到触发断点的生物体
        trigger_organism = None
        if event.organism_id != "population":
            trigger_organism = next(
                (org for org in organisms if org.organism_id == event.organism_id), 
                None
            )
        
        # 生成报告
        report = {
            'metadata': self._generate_metadata(event),
            'breakpoint_analysis': self._analyze_breakpoint(event, trigger_organism),
            'organism_analysis': self._analyze_trigger_organism(trigger_organism) if trigger_organism else None,
            'environment_analysis': self._analyze_environment(grid, organisms),
            'population_analysis': self._analyze_population_context(organisms),
            'challenge_identification': self._identify_challenges(event, trigger_organism, organisms, grid),
            'ai_prompt': self._generate_ai_prompt(event, trigger_organism, organisms, grid)
        }
        
        # 保存报告历史
        self.report_history.append(report)
        
        return report
    
    def _generate_metadata(self, event: BreakpointEvent) -> Dict[str, Any]:
        """生成报告元数据"""
        return {
            'report_id': f"report_{len(self.report_history) + 1}",
            'timestamp': datetime.now().isoformat(),
            'breakpoint_type': event.breakpoint_type.value,
            'trigger_organism': event.organism_id,
            'trigger_value': event.trigger_value,
            'threshold': event.threshold,
            'description': event.description
        }
    
    def _analyze_breakpoint(self, event: BreakpointEvent, organism: Optional[Organism]) -> Dict[str, Any]:
        """分析断点事件本身"""
        analysis = {
            'achievement_type': event.breakpoint_type.value,
            'significance': self._assess_significance(event),
            'rarity': self._assess_rarity(event),
            'context_data': event.context_data
        }
        
        # 根据断点类型添加特定分析
        if event.breakpoint_type == BreakpointType.LONGEVITY:
            analysis['longevity_analysis'] = {
                'survival_duration': event.trigger_value,
                'survival_factors': self._analyze_survival_factors(organism) if organism else None
            }
        elif event.breakpoint_type == BreakpointType.MIGRATION:
            analysis['migration_analysis'] = {
                'total_distance': event.trigger_value,
                'exploration_pattern': self._analyze_exploration_pattern(organism) if organism else None
            }
        elif event.breakpoint_type == BreakpointType.EFFICIENCY:
            analysis['efficiency_analysis'] = {
                'efficiency_ratio': event.trigger_value,
                'optimization_factors': self._analyze_optimization_factors(organism) if organism else None
            }
        
        return analysis
    
    def _analyze_trigger_organism(self, organism: Organism) -> Dict[str, Any]:
        """分析触发断点的生物体"""
        if not organism:
            return {}
        
        stats = organism.get_statistics()
        
        return {
            'basic_info': {
                'organism_id': organism.organism_id,
                'age': stats['age'],
                'energy': stats['energy'],
                'state': stats['state']
            },
            'structure': {
                'unit_count': stats['unit_count'],
                'unit_composition': stats['unit_types'],
                'structure_size': stats['structure_size'],
                'core_position': organism.get_core_position()
            },
            'performance': {
                'total_distance_moved': stats['total_distance_moved'],
                'energy_absorbed_total': stats['energy_absorbed_total'],
                'reproduction_count': stats['reproduction_count']
            },
            'efficiency_metrics': {
                'movement_efficiency': stats['total_distance_moved'] / max(stats['age'], 1),
                'energy_efficiency': stats['energy_absorbed_total'] / max(stats['age'], 1),
                'reproduction_efficiency': stats['reproduction_count'] / max(stats['age'], 1)
            },
            'structural_analysis': self._analyze_organism_structure(organism)
        }
    
    def _analyze_environment(self, grid: Grid, organisms: List[Organism]) -> Dict[str, Any]:
        """分析环境状态"""
        grid_stats = grid.get_statistics()
        energy_map = grid.get_energy_map()
        
        return {
            'grid_info': {
                'dimensions': (grid.width, grid.height),
                'boundary_type': grid.boundary_type.value,
                'total_cells': grid.width * grid.height
            },
            'energy_distribution': {
                'total_energy': grid_stats['total_energy'],
                'average_energy': grid_stats['average_energy'],
                'energy_variance': float(energy_map.var()),
                'energy_max': float(energy_map.max()),
                'energy_min': float(energy_map.min())
            },
            'space_utilization': {
                'occupied_cells': grid_stats['unit_count'],
                'empty_cells': grid_stats['empty_count'],
                'occupancy_rate': grid_stats['occupancy_rate']
            },
            'resource_pressure': self._calculate_resource_pressure(grid, organisms)
        }
    
    def _analyze_population_context(self, organisms: List[Organism]) -> Dict[str, Any]:
        """分析种群背景"""
        if not organisms:
            return {'population_size': 0}
        
        # 基本统计
        total_energy = sum(org.energy for org in organisms)
        total_age = sum(org.age for org in organisms)
        total_units = sum(org.unit_count for org in organisms)
        
        # 结构多样性
        structure_types = {}
        for organism in organisms:
            unit_types = organism.get_statistics()['unit_types']
            structure_key = tuple(sorted(unit_types.items()))
            structure_types[structure_key] = structure_types.get(structure_key, 0) + 1
        
        return {
            'population_size': len(organisms),
            'demographics': {
                'average_age': total_age / len(organisms),
                'average_energy': total_energy / len(organisms),
                'average_structure_size': total_units / len(organisms)
            },
            'diversity': {
                'unique_structures': len(structure_types),
                'diversity_index': len(structure_types) / len(organisms),
                'dominant_structures': sorted(structure_types.items(), key=lambda x: x[1], reverse=True)[:3]
            },
            'collective_performance': {
                'total_reproductions': sum(org.reproduction_count for org in organisms),
                'total_distance_moved': sum(org.total_distance_moved for org in organisms),
                'total_energy_absorbed': sum(org.energy_absorbed_total for org in organisms)
            }
        }
    
    def _identify_challenges(
        self, 
        event: BreakpointEvent, 
        organism: Optional[Organism], 
        organisms: List[Organism], 
        grid: Grid
    ) -> Dict[str, Any]:
        """识别当前面临的挑战"""
        challenges = {
            'environmental_challenges': [],
            'structural_challenges': [],
            'competitive_challenges': [],
            'efficiency_challenges': []
        }
        
        # 环境挑战
        grid_stats = grid.get_statistics()
        if grid_stats['average_energy'] < 10:
            challenges['environmental_challenges'].append("能量稀缺：环境中能量密度较低")
        
        if grid_stats['occupancy_rate'] > 0.8:
            challenges['environmental_challenges'].append("空间拥挤：可用空间不足")
        
        # 结构挑战
        if organism:
            unit_types = organism.get_statistics()['unit_types']
            if unit_types.get('mover', 0) == 0:
                challenges['structural_challenges'].append("缺乏移动能力：无法主动寻找资源")
            
            if unit_types.get('absorber', 0) < 2:
                challenges['structural_challenges'].append("能量获取能力有限：吸收单位不足")
            
            if organism.unit_count > 10:
                challenges['structural_challenges'].append("结构复杂：维持成本高昂")
        
        # 竞争挑战
        if len(organisms) > 20:
            challenges['competitive_challenges'].append("种群竞争激烈：资源竞争加剧")
        
        # 效率挑战
        if organism:
            upkeep_cost = organism.calculate_upkeep_cost()
            if organism.energy < upkeep_cost * 10:
                challenges['efficiency_challenges'].append("能量管理困难：储备不足以应对波动")
        
        return challenges
    
    def _generate_ai_prompt(
        self, 
        event: BreakpointEvent, 
        organism: Optional[Organism], 
        organisms: List[Organism], 
        grid: Grid
    ) -> str:
        """生成AI分析提示词"""
        challenges = self._identify_challenges(event, organism, organisms, grid)
        
        prompt = f"""我正在进行一个元演化生命游戏模拟。当前，一个生物体因为{event.description}而触发了演化断点。

## 断点详情
- 类型：{event.breakpoint_type.value}
- 成就值：{event.trigger_value}
- 阈值：{event.threshold}

## 环境分析
当前环境特征：
- 网格大小：{grid.width}x{grid.height}
- 种群规模：{len(organisms)}个生物体
- 平均能量密度：{grid.get_statistics()['average_energy']:.2f}
- 空间占用率：{grid.get_statistics()['occupancy_rate']:.2%}

## 生物体结构分析
"""
        
        if organism:
            stats = organism.get_statistics()
            prompt += f"""触发断点的生物体结构：
- 单位组成：{stats['unit_types']}
- 总单位数：{stats['unit_count']}
- 年龄：{stats['age']:.1f}回合
- 当前能量：{stats['energy']:.1f}
"""
        
        prompt += f"""
## 主要挑战
基于当前分析，生物体面临的主要挑战包括：
"""
        
        for category, challenge_list in challenges.items():
            if challenge_list:
                prompt += f"\n### {category.replace('_', ' ').title()}\n"
                for challenge in challenge_list:
                    prompt += f"- {challenge}\n"
        
        prompt += f"""
## 任务
基于上述分析，请为我设计一个新的"基本单位"类型。这个单位应该：

1. **功能单一**：符合"基元"的设计哲学，只有一个核心功能
2. **解决挑战**：能够帮助生物体应对当前环境中的主要挑战
3. **平衡性**：有合理的建造成本、维持成本和功能效果
4. **创新性**：提供现有单位类型无法实现的新能力

请描述这个新单位的：
- 名称和核心功能
- 建造成本和维持成本
- 具体的工作机制
- 它如何帮助生物体应对当前挑战
- 可能带来的演化优势

请保持设计简洁而有效，避免过于复杂的功能组合。
"""
        
        return prompt
    
    def _assess_significance(self, event: BreakpointEvent) -> str:
        """评估断点事件的重要性"""
        ratio = event.trigger_value / event.threshold
        
        if ratio >= 2.0:
            return "极高"
        elif ratio >= 1.5:
            return "高"
        elif ratio >= 1.2:
            return "中等"
        else:
            return "低"
    
    def _assess_rarity(self, event: BreakpointEvent) -> str:
        """评估断点事件的稀有性"""
        # 基于历史触发次数评估
        same_type_count = len([e for e in self.report_history 
                              if e.get('metadata', {}).get('breakpoint_type') == event.breakpoint_type.value])
        
        if same_type_count == 0:
            return "首次"
        elif same_type_count <= 2:
            return "罕见"
        elif same_type_count <= 5:
            return "偶尔"
        else:
            return "常见"
    
    def _analyze_survival_factors(self, organism: Organism) -> Dict[str, Any]:
        """分析生存因素"""
        stats = organism.get_statistics()
        return {
            'energy_management': stats['energy'] / max(organism.calculate_upkeep_cost(), 1),
            'structural_efficiency': stats['unit_count'] / max(stats['energy'], 1),
            'mobility': stats['total_distance_moved'] / max(stats['age'], 1)
        }
    
    def _analyze_exploration_pattern(self, organism: Organism) -> Dict[str, Any]:
        """分析探索模式"""
        return {
            'movement_rate': organism.total_distance_moved / max(organism.age, 1),
            'has_mobility': len(organism.get_units_by_type(UnitType.MOVER)) > 0,
            'exploration_efficiency': organism.total_distance_moved / max(organism.energy_absorbed_total, 1)
        }
    
    def _analyze_optimization_factors(self, organism: Organism) -> Dict[str, Any]:
        """分析优化因素"""
        upkeep = organism.calculate_upkeep_cost()
        return {
            'energy_efficiency': organism.energy_absorbed_total / max(upkeep * organism.age, 1),
            'structural_optimization': organism.unit_count / max(organism.energy, 1),
            'resource_utilization': organism.energy / max(upkeep, 1)
        }
    
    def _analyze_organism_structure(self, organism: Organism) -> Dict[str, Any]:
        """分析生物体结构"""
        unit_types = organism.get_statistics()['unit_types']
        
        return {
            'composition_analysis': {
                'core_units': unit_types.get('core', 0),
                'functional_units': sum(unit_types.get(ut, 0) for ut in ['absorber', 'mover']),
                'structural_units': unit_types.get('connector', 0),
                'specialization_ratio': max(unit_types.values()) / max(sum(unit_types.values()), 1)
            },
            'efficiency_metrics': {
                'units_per_energy': organism.unit_count / max(organism.energy, 1),
                'functional_density': (unit_types.get('absorber', 0) + unit_types.get('mover', 0)) / max(organism.unit_count, 1)
            }
        }
    
    def _calculate_resource_pressure(self, grid: Grid, organisms: List[Organism]) -> Dict[str, Any]:
        """计算资源压力"""
        total_demand = sum(org.calculate_upkeep_cost() for org in organisms)
        total_supply = grid.get_statistics()['total_energy']
        
        return {
            'demand_supply_ratio': total_demand / max(total_supply, 1),
            'competition_intensity': len(organisms) / max(grid.width * grid.height, 1),
            'resource_scarcity': 'high' if total_demand > total_supply else 'low'
        }
    
    def save_report(self, report: Dict[str, Any], filename: Optional[str] = None) -> str:
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"data/breakpoint_report_{timestamp}.json"
        
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"报告已保存到: {filename}")
        return filename
