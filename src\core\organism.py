"""
生物体系统 - 由多个单位组成的生命实体

这个模块实现了生物体的结构管理、生命周期、繁殖和突变机制。
生物体是由相互连接的基本单位组成的复合实体。
"""

from typing import Dict, List, Optional, Tuple, Any, Set
from enum import Enum
import uuid
import random
import numpy as np
from .base_unit import BaseUnit, UnitType, create_unit, CoreUnit
from .grid import Grid


class OrganismState(Enum):
    """生物体状态"""
    ALIVE = "alive"
    DEAD = "dead"
    REPRODUCING = "reproducing"


class Organism:
    """
    生物体类
    
    生物体是由多个相互连接的基本单位组成的生命实体。
    每个生物体必须有一个核心单位，其他单位通过连接形成结构。
    """
    
    def __init__(self, organism_id: Optional[str] = None):
        self.organism_id = organism_id or str(uuid.uuid4())
        self.units: Dict[str, BaseUnit] = {}
        self.core_unit: Optional[CoreUnit] = None
        self.energy: float = 0.0
        self.age: float = 0.0
        self.state = OrganismState.ALIVE
        
        # 繁殖相关
        self.reproduction_threshold = 200.0
        self.mutation_rate = 0.05
        
        # 统计信息
        self.total_distance_moved = 0.0
        self.energy_absorbed_total = 0.0
        self.reproduction_count = 0
        
    @property
    def is_alive(self) -> bool:
        """检查是否存活"""
        return self.state == OrganismState.ALIVE and self.core_unit is not None
    
    @property
    def unit_count(self) -> int:
        """获取单位数量"""
        return len(self.units)
    
    @property
    def structure_size(self) -> int:
        """获取结构大小（连接的单位数量）"""
        if not self.core_unit:
            return 0
        return len(self.get_connected_units())
    
    def add_unit(self, unit: BaseUnit) -> bool:
        """
        添加单位到生物体
        
        Args:
            unit: 要添加的单位
            
        Returns:
            是否成功添加
        """
        if unit.unit_id in self.units:
            return False
        
        self.units[unit.unit_id] = unit
        unit.organism_id = self.organism_id
        
        # 如果是核心单位，设置为生物体的核心
        if isinstance(unit, CoreUnit):
            if self.core_unit is None:
                self.core_unit = unit
            else:
                # 一个生物体只能有一个核心
                return False
        
        return True
    
    def remove_unit(self, unit_id: str) -> Optional[BaseUnit]:
        """
        从生物体中移除单位
        
        Args:
            unit_id: 要移除的单位ID
            
        Returns:
            被移除的单位
        """
        unit = self.units.pop(unit_id, None)
        if unit:
            unit.organism_id = None
            
            # 如果移除的是核心单位，生物体死亡
            if unit == self.core_unit:
                self.core_unit = None
                self.state = OrganismState.DEAD
        
        return unit
    
    def get_unit(self, unit_id: str) -> Optional[BaseUnit]:
        """获取指定ID的单位"""
        return self.units.get(unit_id)
    
    def get_units_by_type(self, unit_type: UnitType) -> List[BaseUnit]:
        """获取指定类型的所有单位"""
        return [unit for unit in self.units.values() if unit.unit_type == unit_type]
    
    def get_core_position(self) -> Optional[Tuple[int, int]]:
        """获取核心单位的位置"""
        if self.core_unit:
            return self.core_unit.position
        return None
    
    def get_connected_units(self) -> Set[str]:
        """
        获取所有连接的单位ID集合
        
        使用广度优先搜索从核心单位开始查找所有连接的单位。
        """
        if not self.core_unit:
            return set()
        
        connected = set()
        queue = [self.core_unit.unit_id]
        visited = set()
        
        while queue:
            current_id = queue.pop(0)
            if current_id in visited:
                continue
            
            visited.add(current_id)
            connected.add(current_id)
            
            current_unit = self.units.get(current_id)
            if not current_unit:
                continue
            
            # 查找相邻的同一生物体的单位
            x, y = current_unit.position
            for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                # 这里需要网格信息来查找相邻单位
                # 暂时跳过，在update方法中处理
                pass
        
        return connected
    
    def calculate_upkeep_cost(self) -> float:
        """计算总维持成本"""
        return sum(unit.upkeep_cost for unit in self.units.values())
    
    def update(self, delta_time: float, grid: Grid) -> None:
        """
        更新生物体状态
        
        Args:
            delta_time: 时间增量
            grid: 游戏网格
        """
        if not self.is_alive:
            return
        
        self.age += delta_time
        
        # 更新所有单位
        for unit in self.units.values():
            unit.update(delta_time)
        
        # 执行所有单位的功能
        self._execute_unit_functions(grid)
        
        # 计算并扣除维持成本
        upkeep_cost = self.calculate_upkeep_cost()
        if self.energy >= upkeep_cost:
            self.energy -= upkeep_cost
        else:
            # 能量不足，生物体开始衰亡
            self._handle_energy_shortage(grid)
        
        # 检查繁殖条件
        if self.energy >= self.reproduction_threshold:
            self._attempt_reproduction(grid)
    
    def _execute_unit_functions(self, grid: Grid) -> None:
        """执行所有单位的功能"""
        for unit in self.units.values():
            context = {
                'grid': grid,
                'organism': self,
                'unit': unit
            }
            
            result = unit.execute_function(context)
            self._process_unit_result(unit, result, grid)
    
    def _process_unit_result(self, unit: BaseUnit, result: Dict[str, Any], grid: Grid) -> None:
        """处理单位功能执行结果"""
        action = result.get('action')
        
        if action == 'absorb':
            energy_absorbed = result.get('energy_absorbed', 0.0)
            self.energy += energy_absorbed
            self.energy_absorbed_total += energy_absorbed
        
        elif action == 'move':
            if result.get('success', False):
                direction = result.get('direction', (0, 0))
                energy_cost = result.get('energy_cost', 0.0)
                
                if self.energy >= energy_cost:
                    success = self._move_organism(direction, grid)
                    if success:
                        self.energy -= energy_cost
                        self.total_distance_moved += 1.0
    
    def _move_organism(self, direction: Tuple[int, int], grid: Grid) -> bool:
        """
        移动整个生物体
        
        Args:
            direction: 移动方向
            grid: 游戏网格
            
        Returns:
            是否成功移动
        """
        dx, dy = direction
        
        # 检查所有单位是否都能移动到新位置
        new_positions = []
        for unit in self.units.values():
            x, y = unit.position
            new_x, new_y = x + dx, y + dy
            new_positions.append((unit.unit_id, new_x, new_y))
            
            # 检查新位置是否可用
            target_cell = grid.get_cell(new_x, new_y)
            if not target_cell or not target_cell.is_empty:
                return False
        
        # 如果所有位置都可用，执行移动
        # 首先移除所有单位
        units_to_move = []
        for unit_id, new_x, new_y in new_positions:
            unit = self.units[unit_id]
            old_x, old_y = unit.position
            removed_unit = grid.remove_unit(old_x, old_y)
            units_to_move.append((removed_unit, new_x, new_y))
        
        # 然后放置到新位置
        for unit, new_x, new_y in units_to_move:
            if unit:
                grid.place_unit(unit, new_x, new_y)
        
        return True
    
    def _handle_energy_shortage(self, grid: Grid) -> None:
        """处理能量不足的情况"""
        # 随机移除一个非核心单位
        non_core_units = [unit for unit in self.units.values() 
                         if unit != self.core_unit]
        
        if non_core_units:
            unit_to_remove = random.choice(non_core_units)
            self.remove_unit(unit_to_remove.unit_id)
            
            # 从网格中移除
            x, y = unit_to_remove.position
            grid.remove_unit(x, y)
        else:
            # 没有非核心单位可移除，生物体死亡
            self.state = OrganismState.DEAD
    
    def _attempt_reproduction(self, grid: Grid) -> None:
        """尝试繁殖"""
        if self.state != OrganismState.ALIVE:
            return
        
        # 查找空的相邻位置
        core_pos = self.get_core_position()
        if not core_pos:
            return
        
        x, y = core_pos
        empty_neighbors = []
        
        for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
            neighbor_x, neighbor_y = x + dx, y + dy
            cell = grid.get_cell(neighbor_x, neighbor_y)
            if cell and cell.is_empty:
                empty_neighbors.append((neighbor_x, neighbor_y))
        
        if not empty_neighbors:
            return  # 没有空间繁殖
        
        # 选择一个位置进行繁殖
        reproduction_pos = random.choice(empty_neighbors)
        offspring = self._create_offspring(reproduction_pos, grid)
        
        if offspring:
            # 扣除繁殖成本
            reproduction_cost = self.reproduction_threshold * 0.8
            self.energy -= reproduction_cost
            self.reproduction_count += 1
    
    def _create_offspring(self, position: Tuple[int, int], grid: Grid) -> Optional['Organism']:
        """
        创建后代
        
        Args:
            position: 后代的起始位置
            grid: 游戏网格
            
        Returns:
            创建的后代生物体
        """
        offspring = Organism()
        
        # 复制父代的结构（带突变）
        for unit in self.units.values():
            # 计算相对于核心的位置
            core_pos = self.get_core_position()
            if not core_pos:
                continue
            
            rel_x = unit.position[0] - core_pos[0]
            rel_y = unit.position[1] - core_pos[1]
            
            new_x = position[0] + rel_x
            new_y = position[1] + rel_y
            
            # 检查新位置是否可用
            if not grid.get_cell(new_x, new_y) or not grid.get_cell(new_x, new_y).is_empty:
                continue
            
            # 应用突变
            unit_type = unit.unit_type
            if random.random() < self.mutation_rate:
                # 突变：随机改变单位类型
                all_types = list(UnitType)
                unit_type = random.choice(all_types)
            
            # 创建新单位
            new_unit_id = str(uuid.uuid4())
            new_unit = create_unit(unit_type, new_unit_id, (new_x, new_y))
            
            # 添加到后代和网格
            offspring.add_unit(new_unit)
            grid.place_unit(new_unit, new_x, new_y)
        
        # 给后代一些初始能量
        offspring.energy = self.reproduction_threshold * 0.3
        
        return offspring if offspring.core_unit else None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取生物体统计信息"""
        unit_type_counts = {}
        for unit_type in UnitType:
            unit_type_counts[unit_type.value] = len(self.get_units_by_type(unit_type))
        
        return {
            'organism_id': self.organism_id,
            'age': self.age,
            'energy': self.energy,
            'unit_count': self.unit_count,
            'structure_size': self.structure_size,
            'state': self.state.value,
            'total_distance_moved': self.total_distance_moved,
            'energy_absorbed_total': self.energy_absorbed_total,
            'reproduction_count': self.reproduction_count,
            'unit_types': unit_type_counts
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'organism_id': self.organism_id,
            'units': {uid: unit.to_dict() for uid, unit in self.units.items()},
            'core_unit_id': self.core_unit.unit_id if self.core_unit else None,
            'energy': self.energy,
            'age': self.age,
            'state': self.state.value,
            'statistics': self.get_statistics()
        }
