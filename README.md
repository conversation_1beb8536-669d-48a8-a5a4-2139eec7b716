# 元演化生命游戏 (Meta-Evolution Life Game)

一个结合康威生命游戏和AI辅助演化的创新模拟系统。这个项目探索了"简单规则涌现复杂行为"的核心思想，并引入了"元演化"机制，让AI参与到生命演化的过程中。

## 🎯 项目特色

- **双层演化循环**：内循环是传统的生物演化，外循环是AI辅助的规则创新
- **基本单位系统**：生物体由多种功能单一的基本单位组成
- **实时可视化**：基于Pygame的实时渲染，观察演化过程
- **高性能设计**：使用NumPy优化，支持大规模模拟
- **可扩展架构**：模块化设计，易于添加新功能

## 🚀 快速开始

### 环境要求
- Python 3.12+
- Windows/Linux/macOS

### 安装依赖
```bash
python3 -m pip install -r requirements.txt
```

### 运行测试
```bash
python3 test_core.py
```

### 启动游戏
```bash
python3 main.py
```

## 🎮 游戏控制

- **空格键**：暂停/继续模拟
- **↑/↓ 箭头**：调整模拟速度
- **R键**：重置模拟
- **ESC键**：退出程序

## 🧬 核心概念

### 基本单位类型

1. **核心单位 (Core Unit)**
   - 生物体的中心，每个生物体必须有且只有一个
   - 负责能量存储和结构维持
   - 建造成本：50，维持成本：1

2. **吸收单位 (Absorber Unit)**
   - 从环境中吸收能量
   - 建造成本：20，维持成本：2
   - 吸收效率：5/回合

3. **连接单位 (Connector Unit)**
   - 连接其他单位形成复杂结构
   - 建造成本：10，维持成本：0.5
   - 纯结构功能，无特殊能力

4. **推进单位 (Mover Unit)**
   - 移动整个生物体
   - 建造成本：30，维持成本：3
   - 移动成本：5/次

### 生命周期

1. **出生**：由父代繁殖产生，继承结构（带突变）
2. **生存**：消耗能量维持结构，执行各单位功能
3. **繁殖**：能量达到阈值时自动繁殖
4. **死亡**：能量耗尽或核心单位被摧毁

### 演化机制

- **突变**：繁殖时有5%概率改变单位类型
- **自然选择**：能量不足的生物体逐渐死亡
- **适应性**：不同环境下不同结构的生物体有不同优势

## 📁 项目结构

```
lifegame/
├── src/                    # 源代码
│   ├── core/              # 核心系统
│   │   ├── base_unit.py   # 基本单位系统
│   │   ├── grid.py        # 网格系统
│   │   └── organism.py    # 生物体系统
│   ├── simulation/        # 模拟引擎（待实现）
│   ├── visualization/     # 可视化系统（待实现）
│   ├── ai_integration/    # AI集成（待实现）
│   └── utils/             # 工具模块
│       └── config.py      # 配置管理
├── config/                # 配置文件
│   └── simulation_config.yaml
├── data/                  # 数据存储（待创建）
├── issues/                # 开发记录
├── test_core.py          # 核心系统测试
├── main.py               # 主程序入口
└── requirements.txt      # 依赖列表
```

## ⚙️ 配置说明

主要配置文件：`config/simulation_config.yaml`

### 世界设置
```yaml
world:
  width: 100              # 世界宽度
  height: 100             # 世界高度
  boundary_type: "wrap"   # 边界类型：wrap/wall
```

### 能量系统
```yaml
energy:
  initial_energy_per_cell: 10    # 初始能量密度
  energy_generation_rate: 0.1    # 能量生成速率
  energy_decay_rate: 0.01        # 能量衰减速率
```

### 生物体参数
```yaml
organism:
  initial_energy: 100           # 初始能量
  reproduction_threshold: 200   # 繁殖阈值
  mutation_rate: 0.05          # 突变率
```

## 🔬 开发状态

### ✅ 已完成
- [x] 核心架构设计
- [x] 基本单位系统
- [x] 网格系统
- [x] 生物体系统
- [x] 基础可视化
- [x] 配置管理
- [x] 测试系统

### 🔄 进行中
- [ ] 性能优化（Cython）
- [ ] 断点触发系统
- [ ] AI集成接口

### 📋 计划中
- [ ] Context7 MCP集成
- [ ] 演化历史记录
- [ ] 高级可视化
- [ ] 实验数据导出

## 🎯 未来愿景

### 断点系统
当生物体达成特定里程碑时，模拟暂停并调用AI：
- **生存里程碑**：存活超过N回合
- **迁移里程碑**：移动距离超过N格
- **效率里程碑**：能量转化率达到新高
- **种群里程碑**：某种结构数量超过阈值

### AI创造循环
1. 模拟检测到断点，生成环境分析报告
2. 调用AI分析挑战，提出新的基本单位设计
3. 开发者实现新单位，加入突变池
4. 模拟继续，观察新单位的演化效果

## 🤝 贡献指南

欢迎贡献代码、想法或反馈！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

- 康威生命游戏的启发
- Pygame社区的支持
- AI辅助开发的探索

---

**让我们一起探索生命演化的奥秘！** 🧬✨
