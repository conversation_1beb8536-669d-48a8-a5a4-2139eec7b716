"""
元演化生命游戏 - 带AI集成的主程序

集成了断点系统和AI辅助演化的完整版本。
"""

import sys
import os
import time
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pygame
from typing import List
from core.base_unit import UnitType, create_unit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from ai_integration.breakpoint_manager import BreakpointManager
from utils.config import config


class AIEnhancedLifeGameSimulation:
    """带AI集成的元演化生命游戏"""
    
    def __init__(self):
        # 基础设置
        self.world_width = config.get('world.width', 100)
        self.world_height = config.get('world.height', 100)
        self.window_width = config.get('visualization.window_width', 1200)
        self.window_height = config.get('visualization.window_height', 800)
        self.cell_size = config.get('visualization.grid_cell_size', 8)
        self.fps = config.get('visualization.fps', 60)
        
        # 初始化Pygame
        pygame.init()
        self.screen = pygame.display.set_mode((self.window_width, self.window_height))
        pygame.display.set_caption("元演化生命游戏 - AI增强版")
        self.clock = pygame.time.Clock()
        
        # 创建游戏世界
        boundary_type = BoundaryType.WRAP if config.get('world.boundary_type') == 'wrap' else BoundaryType.WALL
        self.grid = Grid(self.world_width, self.world_height, boundary_type)
        
        # 生物体列表
        self.organisms: List[Organism] = []
        
        # AI断点管理器
        self.breakpoint_manager = BreakpointManager()
        self._setup_breakpoint_callbacks()
        
        # 模拟状态
        self.running = True
        self.paused = False
        self.simulation_speed = 1.0
        self.tick_count = 0
        
        # AI状态
        self.ai_processing = False
        self.pending_ai_units = []
        self.ai_notifications = []
        
        # 颜色定义
        self.colors = {
            'background': (20, 20, 30),
            'grid': (40, 40, 50),
            'energy': (255, 255, 100),
            'core': (255, 100, 100),
            'absorber': (100, 255, 100),
            'connector': (100, 100, 255),
            'mover': (255, 100, 255),
            'text': (255, 255, 255),
            'ai_notification': (255, 200, 0),
            'breakpoint': (255, 50, 50)
        }
        
        # 字体
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.large_font = pygame.font.Font(None, 32)
        
        print("AI增强版游戏初始化完成")
    
    def _setup_breakpoint_callbacks(self):
        """设置断点回调函数"""
        def on_breakpoint(event):
            message = f"🎯 断点触发: {event.description}"
            self.ai_notifications.append({
                'message': message,
                'timestamp': time.time(),
                'type': 'breakpoint'
            })
            print(message)
        
        def on_ai_response(event, unit_design):
            message = f"🤖 AI设计新单位: {unit_design['name']}"
            self.ai_notifications.append({
                'message': message,
                'timestamp': time.time(),
                'type': 'ai_response',
                'unit_design': unit_design
            })
            print(message)
        
        def on_new_unit(unit_design):
            message = f"🆕 新单位已创建: {unit_design['name']}"
            self.ai_notifications.append({
                'message': message,
                'timestamp': time.time(),
                'type': 'new_unit'
            })
            self.pending_ai_units.append(unit_design)
            print(message)
        
        self.breakpoint_manager.set_breakpoint_callback(on_breakpoint)
        self.breakpoint_manager.set_ai_response_callback(on_ai_response)
        self.breakpoint_manager.set_new_unit_callback(on_new_unit)
    
    def create_initial_organisms(self, count: int = 5) -> None:
        """创建初始生物体"""
        print(f"创建 {count} 个初始生物体...")
        
        for i in range(count):
            organism = Organism(f"initial_{i}")
            
            # 随机选择一个空位置
            empty_cells = self.grid.find_empty_cells()
            if not empty_cells:
                break
            
            import random
            start_x, start_y = random.choice(empty_cells)
            
            # 创建基本结构：核心 + 吸收器
            core = create_unit(UnitType.CORE, f"core_{i}", (start_x, start_y))
            absorber = create_unit(UnitType.ABSORBER, f"absorber_{i}", (start_x, start_y + 1))
            
            # 检查位置是否可用
            if (self.grid.place_unit(core, start_x, start_y) and 
                self.grid.place_unit(absorber, start_x, start_y + 1)):
                
                organism.add_unit(core)
                organism.add_unit(absorber)
                organism.energy = config.get('organism.initial_energy', 200)
                
                self.organisms.append(organism)
                print(f"创建生物体 {i+1} 在位置 ({start_x}, {start_y})")
            else:
                print(f"无法放置生物体 {i+1}")
        
        print(f"成功创建 {len(self.organisms)} 个生物体")
    
    async def update_simulation(self, delta_time: float) -> None:
        """更新模拟状态"""
        if self.paused or self.ai_processing:
            return
        
        # 更新网格能量
        self.grid.update_energy(delta_time)
        
        # 更新所有生物体
        alive_organisms = []
        for organism in self.organisms:
            organism.update(delta_time, self.grid)
            if organism.is_alive:
                alive_organisms.append(organism)
            else:
                # 清理死亡生物体的单位
                for unit in organism.units.values():
                    x, y = unit.position
                    self.grid.remove_unit(x, y)
        
        self.organisms = alive_organisms
        
        # 检查断点
        breakpoint_triggered = self.breakpoint_manager.update(self.organisms, self.grid)
        
        if breakpoint_triggered:
            self.ai_processing = True
            print("🤖 开始AI处理...")
            
            # 异步处理断点
            asyncio.create_task(self._process_breakpoints())
        
        self.tick_count += 1
    
    async def _process_breakpoints(self):
        """异步处理断点"""
        try:
            new_units = await self.breakpoint_manager.process_pending_breakpoints(
                self.organisms, self.grid
            )
            
            if new_units:
                message = f"✅ AI处理完成，生成了 {len(new_units)} 个新单位设计"
                self.ai_notifications.append({
                    'message': message,
                    'timestamp': time.time(),
                    'type': 'processing_complete'
                })
                print(message)
            
            # 恢复模拟
            self.breakpoint_manager.resume_simulation()
            self.ai_processing = False
            
        except Exception as e:
            print(f"❌ AI处理出错: {e}")
            self.ai_processing = False
            self.breakpoint_manager.resume_simulation()
    
    def render(self) -> None:
        """渲染游戏画面"""
        self.screen.fill(self.colors['background'])
        
        # 计算网格渲染区域
        grid_width = min(self.world_width * self.cell_size, self.window_width - 300)
        grid_height = min(self.world_height * self.cell_size, self.window_height - 100)
        
        # 渲染网格
        self._render_grid(grid_width, grid_height)
        
        # 渲染UI
        self._render_ui(grid_width + 10)
        
        # 渲染AI通知
        self._render_ai_notifications()
        
        pygame.display.flip()
    
    def _render_grid(self, grid_width: int, grid_height: int) -> None:
        """渲染网格"""
        cell_w = grid_width // self.world_width
        cell_h = grid_height // self.world_height
        
        for y in range(self.world_height):
            for x in range(self.world_width):
                cell = self.grid.get_cell(x, y)
                if not cell:
                    continue
                
                rect_x = x * cell_w
                rect_y = y * cell_h
                rect = pygame.Rect(rect_x, rect_y, cell_w, cell_h)
                
                # 渲染能量（背景色）
                if cell.energy > 0:
                    energy_intensity = min(cell.energy / 50.0, 1.0)
                    energy_color = (
                        int(self.colors['energy'][0] * energy_intensity * 0.3),
                        int(self.colors['energy'][1] * energy_intensity * 0.3),
                        int(self.colors['energy'][2] * energy_intensity * 0.3)
                    )
                    pygame.draw.rect(self.screen, energy_color, rect)
                
                # 渲染单位
                if cell.unit:
                    unit_color = self._get_unit_color(cell.unit.unit_type)
                    pygame.draw.rect(self.screen, unit_color, rect)
                
                # 渲染网格线
                pygame.draw.rect(self.screen, self.colors['grid'], rect, 1)
    
    def _get_unit_color(self, unit_type: UnitType) -> tuple:
        """获取单位颜色"""
        color_map = {
            UnitType.CORE: self.colors['core'],
            UnitType.ABSORBER: self.colors['absorber'],
            UnitType.CONNECTOR: self.colors['connector'],
            UnitType.MOVER: self.colors['mover']
        }
        return color_map.get(unit_type, (255, 255, 255))
    
    def _render_ui(self, start_x: int) -> None:
        """渲染用户界面"""
        y_offset = 20
        
        # 基本信息
        texts = [
            f"Tick: {self.tick_count}",
            f"生物体数量: {len(self.organisms)}",
            f"模拟速度: {self.simulation_speed:.1f}x",
            f"状态: {'AI处理中' if self.ai_processing else '暂停' if self.paused else '运行'}",
            "",
            "控制:",
            "空格 - 暂停/继续",
            "↑/↓ - 调整速度",
            "R - 重置",
            "A - 查看AI状态",
            "ESC - 退出"
        ]
        
        for text in texts:
            if text:  # 跳过空行
                surface = self.small_font.render(text, True, self.colors['text'])
                self.screen.blit(surface, (start_x, y_offset))
            y_offset += 25
        
        # 断点管理器状态
        y_offset += 20
        status = self.breakpoint_manager.get_current_status()
        ai_texts = [
            "AI状态:",
            f"已处理断点: {status['processed_breakpoints']}",
            f"待处理断点: {status['pending_breakpoints']}",
            f"生成单位: {status['ai_stats']['successful_designs']}",
            f"成功率: {status['ai_stats']['success_rate']:.1%}"
        ]
        
        for text in ai_texts:
            surface = self.small_font.render(text, True, self.colors['text'])
            self.screen.blit(surface, (start_x, y_offset))
            y_offset += 20
        
        # 生物体详细信息
        if self.organisms:
            y_offset += 20
            title = self.font.render("生物体信息:", True, self.colors['text'])
            self.screen.blit(title, (start_x, y_offset))
            y_offset += 30
            
            for i, organism in enumerate(self.organisms[:3]):  # 只显示前3个
                stats = organism.get_statistics()
                info_text = f"#{i+1}: E={stats['energy']:.0f} A={stats['age']:.0f} R={stats['reproduction_count']}"
                surface = self.small_font.render(info_text, True, self.colors['text'])
                self.screen.blit(surface, (start_x, y_offset))
                y_offset += 20
    
    def _render_ai_notifications(self) -> None:
        """渲染AI通知"""
        current_time = time.time()
        
        # 清理过期通知（10秒后消失）
        self.ai_notifications = [
            notif for notif in self.ai_notifications 
            if current_time - notif['timestamp'] < 10.0
        ]
        
        # 渲染最近的通知
        y_offset = self.window_height - 150
        for notif in self.ai_notifications[-5:]:  # 最多显示5个
            age = current_time - notif['timestamp']
            alpha = max(0, 255 - int(age * 25))  # 渐变消失
            
            color = self.colors['ai_notification']
            if notif['type'] == 'breakpoint':
                color = self.colors['breakpoint']
            
            # 创建半透明表面
            text_surface = self.small_font.render(notif['message'], True, color)
            text_surface.set_alpha(alpha)
            
            self.screen.blit(text_surface, (10, y_offset))
            y_offset += 25
    
    def handle_events(self) -> None:
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    if not self.ai_processing:
                        self.paused = not self.paused
                elif event.key == pygame.K_UP:
                    self.simulation_speed = min(self.simulation_speed * 1.5, 10.0)
                elif event.key == pygame.K_DOWN:
                    self.simulation_speed = max(self.simulation_speed / 1.5, 0.1)
                elif event.key == pygame.K_r:
                    asyncio.create_task(self._reset_simulation())
                elif event.key == pygame.K_a:
                    self._show_ai_status()
    
    async def _reset_simulation(self) -> None:
        """重置模拟"""
        print("重置模拟...")
        
        # 清理现有生物体
        for organism in self.organisms:
            for unit in organism.units.values():
                x, y = unit.position
                self.grid.remove_unit(x, y)
        
        self.organisms.clear()
        
        # 重置断点管理器
        self.breakpoint_manager.reset()
        
        # 清理AI状态
        self.ai_processing = False
        self.pending_ai_units.clear()
        self.ai_notifications.clear()
        
        # 重新分布能量
        self.grid.distribute_energy(config.get('energy.initial_energy_per_cell', 50) * 
                                   self.world_width * self.world_height)
        
        # 创建新的初始生物体
        self.create_initial_organisms()
        
        self.tick_count = 0
        print("模拟重置完成")
    
    def _show_ai_status(self) -> None:
        """显示AI状态"""
        status = self.breakpoint_manager.get_current_status()
        print("\n=== AI状态报告 ===")
        print(f"断点管理器状态: {'暂停' if status['is_paused'] else '运行'}")
        print(f"已处理断点: {status['processed_breakpoints']}")
        print(f"待处理断点: {status['pending_breakpoints']}")
        print(f"AI调用次数: {status['ai_stats']['total_ai_calls']}")
        print(f"成功设计单位: {status['ai_stats']['successful_designs']}")
        print(f"生成的单位类型: {status['ai_stats']['generated_unit_types']}")
        print("==================")
    
    async def run(self) -> None:
        """运行游戏主循环"""
        print("启动AI增强版游戏循环...")
        
        # 初始化世界
        self.grid.distribute_energy(config.get('energy.initial_energy_per_cell', 50) * 
                                   self.world_width * self.world_height)
        self.create_initial_organisms()
        
        last_time = time.time()
        
        while self.running:
            current_time = time.time()
            delta_time = (current_time - last_time) * self.simulation_speed
            last_time = current_time
            
            # 处理事件
            self.handle_events()
            
            # 更新模拟
            await self.update_simulation(delta_time)
            
            # 渲染
            self.render()
            
            # 控制帧率
            self.clock.tick(self.fps)
        
        pygame.quit()
        print("游戏结束")


async def main():
    """主函数"""
    print("元演化生命游戏 - AI增强版启动")
    print("=" * 60)
    
    try:
        # 创建并运行游戏
        game = AIEnhancedLifeGameSimulation()
        await game.run()
        
    except Exception as e:
        print(f"游戏运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("程序退出")


if __name__ == "__main__":
    asyncio.run(main())
