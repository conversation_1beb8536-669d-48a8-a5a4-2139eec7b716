"""
储能单位 - 高效能量存储，在能量充足时储存，在稀缺时释放

由AI设计的新单位类型。
储能单位会自动监测生物体的能量状态。当生物体能量充足(>80%容量)时，储能单位会将多余能量储存起来。当生物体能量不足(<30%容量)时，储能单位会释放储存的能量。
"""

from typing import Dict, Any, Tuple
from core.base_unit import BaseUnit, UnitType


class StorageUnitUnit(BaseUnit):
    """
    储能单位
    
    高效能量存储，在能量充足时储存，在稀缺时释放
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.STORAGEUNIT
    
    @property
    def build_cost(self) -> float:
        return 40.0
    
    @property
    def upkeep_cost(self) -> float:
        return 0.5
    
    @property
    def max_health(self) -> float:
        return 50
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        super().__init__(unit_id, position)
        # 添加特定属性
        self.storage_capacity = 150
        self.charge_rate = 10
        self.discharge_rate = 15

    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行储能单位的功能"""
        # TODO: 实现具体功能逻辑
        return {
            'action': 'storageunit',
            'unit_type': '储能单位',
            'description': '高效能量存储，在能量充足时储存，在稀缺时释放'
        }
