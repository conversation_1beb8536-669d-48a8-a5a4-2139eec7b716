"""
DeepSeek API测试

测试与DeepSeek AI服务的集成，验证真实AI响应。
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.base_unit import UnitType, create_unit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from ai_integration.breakpoint_detector import BreakpointDetector, BreakpointType
from ai_integration.context_reporter import ContextReporter
from ai_integration.ai_integrator import AIIntegrator
from utils.config import config


def create_test_scenario_for_ai():
    """创建用于AI测试的场景"""
    print("=== 创建AI测试场景 ===")
    
    # 创建网格
    grid = Grid(30, 30, BoundaryType.WRAP)
    grid.distribute_energy(1000)
    
    # 创建一个面临能量稀缺挑战的生物体
    organism = Organism("energy_challenge_test")
    
    # 创建结构：核心 + 多个吸收器（高能耗）
    core = create_unit(UnitType.CORE, "core_1", (15, 15))
    absorber1 = create_unit(UnitType.ABSORBER, "absorber_1", (15, 16))
    absorber2 = create_unit(UnitType.ABSORBER, "absorber_2", (14, 15))
    absorber3 = create_unit(UnitType.ABSORBER, "absorber_3", (16, 15))
    mover = create_unit(UnitType.MOVER, "mover_1", (15, 14))
    
    # 添加到生物体
    organism.add_unit(core)
    organism.add_unit(absorber1)
    organism.add_unit(absorber2)
    organism.add_unit(absorber3)
    organism.add_unit(mover)
    
    # 放置到网格
    grid.place_unit(core, 15, 15)
    grid.place_unit(absorber1, 15, 16)
    grid.place_unit(absorber2, 14, 15)
    grid.place_unit(absorber3, 16, 15)
    grid.place_unit(mover, 15, 14)
    
    # 设置挑战性的状态
    organism.energy = 100.0  # 相对较低的能量
    organism.age = 1500.0    # 触发生存断点
    organism.total_distance_moved = 800.0  # 触发迁移断点
    organism.energy_absorbed_total = 1500.0
    organism.reproduction_count = 5
    
    # 降低环境能量，制造稀缺
    for y in range(grid.height):
        for x in range(grid.width):
            cell = grid.get_cell(x, y)
            if cell:
                cell.energy *= 0.3  # 减少70%的环境能量
    
    print(f"创建挑战性测试生物体: {organism.organism_id}")
    print(f"  年龄: {organism.age} (触发生存断点)")
    print(f"  移动距离: {organism.total_distance_moved} (触发迁移断点)")
    print(f"  当前能量: {organism.energy}")
    print(f"  维持成本: {organism.calculate_upkeep_cost()}")
    print(f"  单位数量: {organism.unit_count}")
    print(f"  环境能量密度: {grid.get_statistics()['average_energy']:.2f}")
    
    return grid, [organism]


async def test_deepseek_api_basic():
    """测试DeepSeek API基本功能"""
    print("\n=== 测试DeepSeek API基本功能 ===")
    
    ai_integrator = AIIntegrator()
    
    # 简单的测试提示
    test_prompt = """请设计一个游戏中的新单位类型。

要求：
1. 名称：能量收集器 (Energy Collector)
2. 功能：提高能量收集效率
3. 建造成本：30能量
4. 维持成本：1.5能量/回合

请按照以下格式回复：
**名称**: 能量收集器 (Energy Collector)
**核心功能**: 提高能量收集效率
**技术规格**:
- 建造成本: 30能量
- 维持成本: 1.5能量/回合
- 收集效率: 提升50%

**工作机制**:
详细描述工作原理...

**应对挑战**:
解决的问题...

**演化优势**:
带来的优势..."""
    
    try:
        print("🔄 发送测试请求到DeepSeek API...")
        response = await ai_integrator._call_ai_service(test_prompt)
        
        if response:
            print("✅ API调用成功！")
            print(f"响应长度: {len(response)} 字符")
            print(f"响应预览 (前500字符):")
            print("-" * 50)
            print(response[:500])
            print("-" * 50)
            
            # 尝试解析响应
            unit_design = ai_integrator._parse_ai_response(response)
            if unit_design:
                print(f"✅ 响应解析成功!")
                print(f"单位名称: {unit_design['name']}")
                print(f"英文名称: {unit_design['english_name']}")
                print(f"核心功能: {unit_design['function']}")
                print(f"建造成本: {unit_design['build_cost']}")
                print(f"维持成本: {unit_design['upkeep_cost']}")
                return unit_design
            else:
                print("❌ 响应解析失败")
        else:
            print("❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    return None


async def test_deepseek_with_real_scenario():
    """使用真实场景测试DeepSeek API"""
    print("\n=== 使用真实场景测试DeepSeek API ===")
    
    # 创建测试场景
    grid, organisms = create_test_scenario_for_ai()
    
    # 创建AI集成器
    ai_integrator = AIIntegrator()
    detector = BreakpointDetector()
    
    # 检测断点
    events = detector.check_breakpoints(organisms, grid)
    
    if events:
        print(f"检测到 {len(events)} 个断点")
        
        # 选择第一个断点进行AI处理
        event = events[0]
        print(f"处理断点: {event.description}")
        
        try:
            # 调用AI处理
            unit_design = await ai_integrator.process_breakpoint(event, organisms, grid)
            
            if unit_design:
                print("🎉 DeepSeek AI成功设计新单位!")
                print(f"单位名称: {unit_design['name']}")
                print(f"英文名称: {unit_design['english_name']}")
                print(f"核心功能: {unit_design['function']}")
                print(f"建造成本: {unit_design['build_cost']}")
                print(f"维持成本: {unit_design['upkeep_cost']}")
                
                # 显示AI的完整响应
                print(f"\n🤖 AI完整响应:")
                print("-" * 60)
                print(unit_design['raw_response'])
                print("-" * 60)
                
                # 生成并保存代码
                code = ai_integrator.get_unit_design_code(unit_design)
                print(f"\n💻 生成的Python代码 (前300字符):")
                print(code[:300] + "...")
                
                # 保存单位设计
                filename = ai_integrator.save_unit_design(unit_design)
                print(f"📁 单位代码已保存: {filename}")
                
                return unit_design
            else:
                print("❌ AI处理失败")
                
        except Exception as e:
            print(f"❌ AI处理过程中出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ 没有检测到断点")
    
    return None


async def test_api_configuration():
    """测试API配置"""
    print("\n=== 测试API配置 ===")
    
    print(f"API URL: {config.get('ai_integration.api_url')}")
    print(f"模型: {config.get('ai_integration.model')}")
    print(f"温度: {config.get('ai_integration.temperature')}")
    print(f"最大令牌: {config.get('ai_integration.max_tokens')}")
    print(f"回退到模拟: {config.get('ai_integration.fallback_to_mock')}")
    
    # 检查API密钥（只显示前几位）
    api_key = config.get('ai_integration.api_key', '')
    if api_key:
        print(f"API密钥: {api_key[:10]}...{api_key[-4:]} (已配置)")
    else:
        print("❌ API密钥未配置")


async def main():
    """主测试函数"""
    print("DeepSeek API集成测试")
    print("=" * 60)
    
    try:
        # 1. 测试配置
        await test_api_configuration()
        
        # 2. 基本API测试
        basic_result = await test_deepseek_api_basic()
        
        # 3. 真实场景测试
        if basic_result:
            scenario_result = await test_deepseek_with_real_scenario()
            
            if scenario_result:
                print("\n🎉 所有测试通过！DeepSeek API集成成功！")
                
                # 比较两次AI响应
                print(f"\n📊 测试结果对比:")
                print(f"基本测试单位: {basic_result['name']}")
                print(f"场景测试单位: {scenario_result['name']}")
                
                if basic_result['name'] != scenario_result['name']:
                    print("✅ AI能够根据不同场景生成不同的设计")
                else:
                    print("ℹ️ AI生成了相似的设计")
            else:
                print("⚠️ 场景测试失败，但基本功能正常")
        else:
            print("❌ 基本API测试失败")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
