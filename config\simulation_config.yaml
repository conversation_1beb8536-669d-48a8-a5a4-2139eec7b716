# 元演化生命游戏配置文件

# 世界设置
world:
  width: 100
  height: 100
  boundary_type: "wrap"  # "wrap" 或 "wall"
  
# 能量系统
energy:
  initial_energy_per_cell: 50
  energy_generation_rate: 0.5
  energy_decay_rate: 0.001
  max_energy_per_cell: 200

# 基本单位设置
units:
  core:
    build_cost: 50
    upkeep_cost: 1
    max_health: 100
  absorber:
    build_cost: 20
    upkeep_cost: 2
    absorption_rate: 5
  connector:
    build_cost: 10
    upkeep_cost: 0.5
  mover:
    build_cost: 30
    upkeep_cost: 3
    move_cost: 5

# 生物体设置
organism:
  initial_energy: 200
  reproduction_threshold: 400
  mutation_rate: 0.05
  max_size: 50

# 断点触发条件
breakpoints:
  longevity_threshold: 1000
  population_threshold: 50
  migration_threshold: 500
  efficiency_threshold: 0.8

# 可视化设置
visualization:
  window_width: 1200
  window_height: 800
  grid_cell_size: 8
  fps: 60
  show_energy: true
  show_connections: true

# 性能设置
performance:
  use_cython: false
  max_organisms: 1000
  simulation_threads: 1

# AI集成设置
ai_integration:
  provider: "deepseek"
  api_url: "https://api.deepseek.com/v1/chat/completions"
  api_key: "***********************************"
  model: "deepseek-chat"
  temperature: 0.7
  max_tokens: 2000
  auto_save_units: true
  fallback_to_mock: true
