"""
AI集成器

负责与AI服务（Context7 MCP）进行交互，获取新单位设计建议。
"""

from typing import Dict, List, Any, Optional
import json
import re
import aiohttp
import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from ai_integration.context_reporter import ContextReporter
from ai_integration.breakpoint_detector import BreakpointEvent
from core.organism import Organism
from core.grid import Grid
from utils.config import config


class AIIntegrator:
    """
    AI集成器

    与AI服务交互，获取新单位设计建议并解析响应。
    """

    def __init__(self):
        self.context_reporter = ContextReporter()
        self.ai_responses: List[Dict[str, Any]] = []
        self.generated_units: List[Dict[str, Any]] = []

        # DeepSeek API配置
        self.api_url = config.get('ai_integration.api_url', 'https://api.deepseek.com/v1/chat/completions')
        self.api_key = config.get('ai_integration.api_key', 'sk-12206444d7b743ca84f82d53b82438c5')
        self.model = config.get('ai_integration.model', 'deepseek-chat')
        self.temperature = config.get('ai_integration.temperature', 0.7)
        self.max_tokens = config.get('ai_integration.max_tokens', 2000)
        self.fallback_to_mock = config.get('ai_integration.fallback_to_mock', True)

        print("AI集成器初始化完成 - 使用DeepSeek API")
    
    async def process_breakpoint(
        self, 
        event: BreakpointEvent, 
        organisms: List[Organism], 
        grid: Grid
    ) -> Optional[Dict[str, Any]]:
        """
        处理断点事件，调用AI获取新单位设计
        
        Args:
            event: 断点事件
            organisms: 当前生物体列表
            grid: 游戏网格
            
        Returns:
            AI建议的新单位设计，如果失败则返回None
        """
        print(f"🤖 处理断点事件: {event.description}")
        
        # 生成上下文报告
        report = self.context_reporter.generate_breakpoint_report(event, organisms, grid)
        
        # 保存报告
        report_file = self.context_reporter.save_report(report)
        
        # 调用AI服务
        ai_response = await self._call_ai_service(report['ai_prompt'])
        
        if ai_response:
            # 直接保存AI响应，不进行解析
            interaction_record = {
                'breakpoint_event': {
                    'breakpoint_type': event.breakpoint_type.value,
                    'organism_id': event.organism_id,
                    'trigger_value': event.trigger_value,
                    'threshold': event.threshold,
                    'description': event.description,
                    'timestamp': event.timestamp
                },
                'report_file': report_file,
                'ai_response': ai_response,
                'timestamp': report['metadata']['timestamp'],
                'status': 'awaiting_compilation'
            }

            self.ai_responses.append(interaction_record)

            # 保存AI响应到文件
            response_file = self._save_ai_response(ai_response, event)

            print(f"✅ AI响应已保存，等待手动编译")
            print(f"📁 响应文件: {response_file}")
            print(f"🎯 断点类型: {event.breakpoint_type.value}")
            print(f"📝 描述: {event.description}")

            return {
                'status': 'awaiting_compilation',
                'response_file': response_file,
                'ai_response': ai_response,
                'breakpoint_info': {
                    'type': event.breakpoint_type.value,
                    'description': event.description,
                    'organism_id': event.organism_id
                }
            }
        else:
            print("❌ AI服务调用失败")
        
        return None

    def _save_ai_response(self, ai_response: str, event: BreakpointEvent) -> str:
        """
        保存AI响应到文件

        Args:
            ai_response: AI的响应文本
            event: 断点事件

        Returns:
            保存的文件路径
        """
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"data/ai_response_{event.breakpoint_type.value}_{timestamp}.txt"

        # 创建响应文件内容
        content = f"""# AI响应 - {event.breakpoint_type.value}
# 时间: {timestamp}
# 断点描述: {event.description}
# 生物体ID: {event.organism_id}
# 触发值: {event.trigger_value}
# 阈值: {event.threshold}

==================== AI响应内容 ====================

{ai_response}

==================== 编译说明 ====================

请根据上述AI响应手动创建新的单位类型：

1. 在 src/core/base_unit.py 中添加新的 UnitType 枚举值
2. 创建新的单位类，继承自 BaseUnit
3. 实现所需的属性和方法
4. 在 create_unit 函数中添加新单位的创建逻辑
5. 更新配置文件中的单位参数

完成后，新单位将自动加入到突变池中。
"""

        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"AI响应已保存到: {filename}")
        return filename
    
    async def _call_ai_service(self, prompt: str) -> Optional[str]:
        """
        调用DeepSeek AI服务

        Args:
            prompt: 发送给AI的提示词

        Returns:
            AI的响应文本，如果失败则返回None
        """
        try:
            print("🔄 调用DeepSeek AI服务...")

            # 准备请求数据
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的游戏设计师和程序员，擅长设计创新的游戏机制和单位。请根据用户的需求设计新的游戏单位，要求功能明确、平衡性好、有创新性。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }

            # 发送异步HTTP请求
            timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                print(f"🌐 发送请求到: {self.api_url}")
                async with session.post(self.api_url, headers=headers, json=data) as response:
                    print(f"📡 收到响应，状态码: {response.status}")
                    if response.status == 200:
                        result = await response.json()
                        ai_response = result['choices'][0]['message']['content']
                        print(f"✅ DeepSeek AI服务响应成功，响应长度: {len(ai_response)}")
                        return ai_response
                    else:
                        error_text = await response.text()
                        print(f"❌ AI服务返回错误 {response.status}: {error_text}")
                        return None

        except Exception as e:
            print(f"❌ AI服务调用失败: {e}")
            # 如果API调用失败且允许回退，使用模拟响应
            if self.fallback_to_mock:
                print("🔄 回退到模拟AI响应...")
                return self._generate_mock_ai_response(prompt)
            else:
                return None
    
    def _generate_mock_ai_response(self, prompt: str) -> str:
        """
        生成模拟AI响应（用于测试）
        
        实际部署时应该替换为真实的Context7 MCP调用
        """
        # 根据提示词中的关键词生成不同的响应
        if "能量稀缺" in prompt or "energy" in prompt.lower():
            return """
基于您的分析，我建议设计一个"储能单位"(Storage Unit)：

**名称**: 储能单位 (Storage Unit)
**核心功能**: 高效能量存储，在能量充足时储存，在稀缺时释放

**技术规格**:
- 建造成本: 40能量
- 维持成本: 0.5能量/回合
- 储存容量: 150能量
- 充电效率: 每回合可储存10能量
- 放电效率: 每回合可释放15能量

**工作机制**:
储能单位会自动监测生物体的能量状态。当生物体能量充足(>80%容量)时，储能单位会将多余能量储存起来。当生物体能量不足(<30%容量)时，储能单位会释放储存的能量。

**应对挑战**:
- 解决能量波动问题，让生物体能够度过资源稀缺期
- 提供能量缓冲，支持长距离迁移
- 降低对环境能量密度的依赖

**演化优势**:
拥有储能单位的生物体可以在恶劣环境中生存更久，有更多时间寻找新的资源区域，从而获得生存优势。
"""
        
        elif "移动" in prompt or "migration" in prompt.lower():
            return """
基于您的分析，我建议设计一个"感知单位"(Sensor Unit)：

**名称**: 感知单位 (Sensor Unit)  
**核心功能**: 远程感知环境能量分布，指导移动方向

**技术规格**:
- 建造成本: 35能量
- 维持成本: 2能量/回合
- 感知范围: 5格半径
- 精度: 可检测能量密度差异>5

**工作机制**:
感知单位可以探测周围5格范围内的能量分布，并向生物体的移动单位提供方向指导。它会计算最优路径，引导生物体向能量密度更高的区域移动。

**应对挑战**:
- 提高移动效率，避免盲目探索
- 减少无效移动的能量浪费
- 帮助生物体快速找到资源丰富区域

**演化优势**:
拥有感知单位的生物体可以进行有目标的移动，大大提高觅食效率，在竞争中占据优势。
"""
        
        elif "竞争" in prompt or "competition" in prompt.lower():
            return """
基于您的分析，我建议设计一个"防护单位"(Shield Unit)：

**名称**: 防护单位 (Shield Unit)
**核心功能**: 保护生物体免受碰撞损伤，提供结构稳定性

**技术规格**:
- 建造成本: 25能量
- 维持成本: 1能量/回合
- 防护值: 50生命值
- 损伤吸收: 优先承受碰撞伤害

**工作机制**:
防护单位位于生物体外围，当生物体与其他生物体或障碍物碰撞时，防护单位会优先承受伤害。当防护单位被摧毁时，会给生物体一个回合的时间来逃离危险区域。

**应对挑战**:
- 在拥挤环境中保护核心结构
- 减少碰撞造成的结构损失
- 提供竞争中的生存优势

**演化优势**:
在高密度种群环境中，拥有防护单位的生物体能够更好地保护自己的结构完整性，提高生存率。
"""
        
        else:
            return """
基于您的分析，我建议设计一个"效率单位"(Efficiency Unit)：

**名称**: 效率单位 (Efficiency Unit)
**核心功能**: 优化生物体的能量使用效率

**技术规格**:
- 建造成本: 30能量
- 维持成本: 1.5能量/回合
- 效率提升: 降低其他单位15%的维持成本
- 作用范围: 相邻的所有单位

**工作机制**:
效率单位通过优化能量分配和使用，降低相邻单位的维持成本。每个效率单位可以影响与其相邻的所有单位，但多个效率单位的效果不叠加。

**应对挑战**:
- 降低大型结构的维持成本
- 提高能量使用效率
- 支持更复杂的生物体结构

**演化优势**:
拥有效率单位的生物体可以维持更大的结构，或者在相同结构下消耗更少能量，从而获得竞争优势。
"""
    
    def _parse_ai_response(self, response: str) -> Optional[Dict[str, Any]]:
        """
        解析AI响应，提取单位设计信息
        
        Args:
            response: AI的响应文本
            
        Returns:
            解析出的单位设计字典，如果解析失败则返回None
        """
        try:
            # 使用正则表达式提取关键信息
            name_match = re.search(r'\*\*名称\*\*[：:]\s*([^(]+)\s*\(([^)]+)\)', response)
            function_match = re.search(r'\*\*核心功能\*\*[：:]\s*([^\n]+)', response)
            build_cost_match = re.search(r'建造成本[：:]\s*(\d+)', response)
            upkeep_cost_match = re.search(r'维持成本[：:]\s*([\d.]+)', response)
            
            if not all([name_match, function_match, build_cost_match, upkeep_cost_match]):
                print("❌ 无法解析AI响应中的关键信息")
                return None
            
            # 提取信息
            chinese_name = name_match.group(1).strip()
            english_name = name_match.group(2).strip()
            function = function_match.group(1).strip()
            build_cost = float(build_cost_match.group(1))
            upkeep_cost = float(upkeep_cost_match.group(1))
            
            # 提取其他属性
            attributes = {}
            
            # 查找技术规格部分
            tech_specs_match = re.search(r'\*\*技术规格\*\*[：:](.+?)\*\*工作机制\*\*', response, re.DOTALL)
            if tech_specs_match:
                tech_specs = tech_specs_match.group(1)
                
                # 提取各种属性
                for line in tech_specs.split('\n'):
                    line = line.strip()
                    if ':' in line or '：' in line:
                        key_value = re.split('[：:]', line, 1)
                        if len(key_value) == 2:
                            key = key_value[0].strip('- ').strip()
                            value = key_value[1].strip()
                            
                            # 尝试转换为数字
                            try:
                                if '.' in value:
                                    attributes[key] = float(re.search(r'[\d.]+', value).group())
                                else:
                                    num_match = re.search(r'\d+', value)
                                    if num_match:
                                        attributes[key] = int(num_match.group())
                                    else:
                                        attributes[key] = value
                            except:
                                attributes[key] = value
            
            # 提取工作机制
            mechanism_match = re.search(r'\*\*工作机制\*\*[：:](.+?)\*\*应对挑战\*\*', response, re.DOTALL)
            mechanism = mechanism_match.group(1).strip() if mechanism_match else ""
            
            # 提取应对挑战
            challenges_match = re.search(r'\*\*应对挑战\*\*[：:](.+?)\*\*演化优势\*\*', response, re.DOTALL)
            challenges = challenges_match.group(1).strip() if challenges_match else ""
            
            # 提取演化优势
            advantages_match = re.search(r'\*\*演化优势\*\*[：:](.+?)$', response, re.DOTALL)
            advantages = advantages_match.group(1).strip() if advantages_match else ""
            
            unit_design = {
                'name': chinese_name,
                'english_name': english_name,
                'function': function,
                'build_cost': build_cost,
                'upkeep_cost': upkeep_cost,
                'attributes': attributes,
                'mechanism': mechanism,
                'challenges_addressed': challenges,
                'evolutionary_advantages': advantages,
                'raw_response': response
            }
            
            print(f"✅ 成功解析单位设计: {chinese_name}")
            return unit_design
            
        except Exception as e:
            print(f"❌ 解析AI响应时出错: {e}")
            return None
    
    def get_unit_design_code(self, unit_design: Dict[str, Any]) -> str:
        """
        根据单位设计生成Python代码
        
        Args:
            unit_design: 单位设计字典
            
        Returns:
            生成的Python类代码
        """
        english_name = unit_design['english_name'].replace(' ', '')
        class_name = f"{english_name}Unit"
        
        code = f'''"""
{unit_design['name']} - {unit_design['function']}

由AI设计的新单位类型。
{unit_design['mechanism']}
"""

from typing import Dict, Any, Tuple
from core.base_unit import BaseUnit, UnitType


class {class_name}(BaseUnit):
    """
    {unit_design['name']}
    
    {unit_design['function']}
    """
    
    @property
    def unit_type(self) -> UnitType:
        return UnitType.{english_name.upper()}
    
    @property
    def build_cost(self) -> float:
        return {unit_design['build_cost']}
    
    @property
    def upkeep_cost(self) -> float:
        return {unit_design['upkeep_cost']}
    
    @property
    def max_health(self) -> float:
        return {unit_design['attributes'].get('生命值', 50)}
    
    def __init__(self, unit_id: str, position: Tuple[int, int]):
        super().__init__(unit_id, position)
        # 添加特定属性
'''
        
        # 添加特定属性
        for key, value in unit_design['attributes'].items():
            if key not in ['建造成本', '维持成本', '生命值']:
                attr_name = self._convert_to_python_name(key)
                code += f"        self.{attr_name} = {repr(value)}\n"
        
        code += f'''
    def execute_function(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行{unit_design['name']}的功能"""
        # TODO: 实现具体功能逻辑
        return {{
            'action': '{english_name.lower()}',
            'unit_type': '{unit_design['name']}',
            'description': '{unit_design['function']}'
        }}
'''
        
        return code
    
    def _convert_to_python_name(self, chinese_name: str) -> str:
        """将中文属性名转换为Python变量名"""
        name_map = {
            '储存容量': 'storage_capacity',
            '感知范围': 'sensor_range',
            '防护值': 'shield_value',
            '效率提升': 'efficiency_boost',
            '作用范围': 'effect_range',
            '充电效率': 'charge_rate',
            '放电效率': 'discharge_rate',
            '精度': 'precision',
            '损伤吸收': 'damage_absorption'
        }
        
        return name_map.get(chinese_name, chinese_name.lower().replace(' ', '_'))
    
    def save_unit_design(self, unit_design: Dict[str, Any], filename: Optional[str] = None) -> str:
        """保存单位设计到文件"""
        if not filename:
            safe_name = unit_design['english_name'].replace(' ', '_').lower()
            filename = f"src/core/units/{safe_name}_unit.py"
        
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        code = self.get_unit_design_code(unit_design)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)
        
        print(f"单位代码已保存到: {filename}")
        return filename
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取AI集成器统计信息"""
        return {
            'total_ai_calls': len(self.ai_responses),
            'successful_designs': len(self.generated_units),
            'success_rate': len(self.generated_units) / max(len(self.ai_responses), 1),
            'generated_unit_types': [unit['name'] for unit in self.generated_units]
        }
