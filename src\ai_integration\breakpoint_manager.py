"""
断点管理器

协调断点检测、AI集成和新单位创建的整个流程。
"""

from typing import Dict, List, Any, Optional, Callable
import asyncio
import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from ai_integration.breakpoint_detector import BreakpointDetector, BreakpointEvent
from ai_integration.ai_integrator import AIIntegrator
from core.organism import Organism
from core.grid import Grid
from utils.config import config


class BreakpointManager:
    """
    断点管理器
    
    协调整个元演化流程：检测断点 -> 调用AI -> 创建新单位 -> 继续模拟
    """
    
    def __init__(self):
        self.detector = BreakpointDetector()
        self.ai_integrator = AIIntegrator()
        
        # 状态管理
        self.is_paused = False
        self.pending_breakpoints: List[BreakpointEvent] = []
        self.processed_breakpoints: List[BreakpointEvent] = []
        
        # 回调函数
        self.on_breakpoint_triggered: Optional[Callable] = None
        self.on_ai_response_received: Optional[Callable] = None
        self.on_new_unit_created: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_breakpoints': 0,
            'processed_breakpoints': 0,
            'successful_ai_calls': 0,
            'created_units': 0,
            'simulation_pauses': 0
        }
        
        print("断点管理器初始化完成")
    
    def update(self, organisms: List[Organism], grid: Grid) -> bool:
        """
        更新断点检测
        
        Args:
            organisms: 当前生物体列表
            grid: 游戏网格
            
        Returns:
            是否触发了断点（需要暂停模拟）
        """
        if self.is_paused:
            return True
        
        # 检测断点
        triggered_events = self.detector.check_breakpoints(organisms, grid)
        
        if triggered_events:
            self.stats['total_breakpoints'] += len(triggered_events)
            self.pending_breakpoints.extend(triggered_events)
            
            # 触发回调
            if self.on_breakpoint_triggered:
                for event in triggered_events:
                    self.on_breakpoint_triggered(event)
            
            # 暂停模拟以处理断点
            self.is_paused = True
            self.stats['simulation_pauses'] += 1
            
            print(f"🛑 模拟暂停：检测到 {len(triggered_events)} 个断点")
            return True
        
        return False
    
    async def process_pending_breakpoints(self, organisms: List[Organism], grid: Grid) -> List[Dict[str, Any]]:
        """
        处理所有待处理的断点
        
        Args:
            organisms: 当前生物体列表
            grid: 游戏网格
            
        Returns:
            生成的新单位设计列表
        """
        if not self.pending_breakpoints:
            return []
        
        print(f"🔄 开始处理 {len(self.pending_breakpoints)} 个断点...")
        
        new_unit_designs = []
        
        for event in self.pending_breakpoints:
            print(f"\n处理断点: {event.description}")
            
            try:
                # 调用AI处理断点
                unit_design = await self.ai_integrator.process_breakpoint(event, organisms, grid)
                
                if unit_design:
                    new_unit_designs.append(unit_design)
                    self.stats['successful_ai_calls'] += 1
                    
                    # 触发回调
                    if self.on_ai_response_received:
                        self.on_ai_response_received(event, unit_design)
                    
                    # 保存单位代码（可选）
                    if config.get('ai_integration.auto_save_units', True):
                        self.ai_integrator.save_unit_design(unit_design)
                        self.stats['created_units'] += 1
                        
                        if self.on_new_unit_created:
                            self.on_new_unit_created(unit_design)
                
                # 标记为已处理
                self.processed_breakpoints.append(event)
                self.stats['processed_breakpoints'] += 1
                
            except Exception as e:
                print(f"❌ 处理断点时出错: {e}")
                continue
        
        # 清空待处理列表
        self.pending_breakpoints.clear()
        
        print(f"✅ 断点处理完成，生成了 {len(new_unit_designs)} 个新单位设计")
        return new_unit_designs
    
    def resume_simulation(self) -> None:
        """恢复模拟"""
        if self.is_paused:
            self.is_paused = False
            print("▶️ 模拟恢复")
    
    def pause_simulation(self) -> None:
        """暂停模拟"""
        if not self.is_paused:
            self.is_paused = True
            self.stats['simulation_pauses'] += 1
            print("⏸️ 模拟暂停")
    
    def set_breakpoint_callback(self, callback: Callable[[BreakpointEvent], None]) -> None:
        """设置断点触发回调"""
        self.on_breakpoint_triggered = callback
    
    def set_ai_response_callback(self, callback: Callable[[BreakpointEvent, Dict[str, Any]], None]) -> None:
        """设置AI响应回调"""
        self.on_ai_response_received = callback
    
    def set_new_unit_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置新单位创建回调"""
        self.on_new_unit_created = callback
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            'is_paused': self.is_paused,
            'pending_breakpoints': len(self.pending_breakpoints),
            'processed_breakpoints': len(self.processed_breakpoints),
            'detector_stats': self.detector.get_statistics(),
            'ai_stats': self.ai_integrator.get_statistics(),
            'manager_stats': self.stats
        }
    
    def get_breakpoint_history(self) -> List[Dict[str, Any]]:
        """获取断点历史"""
        history = []
        
        for event in self.processed_breakpoints:
            history.append({
                'timestamp': event.timestamp,
                'type': event.breakpoint_type.value,
                'organism_id': event.organism_id,
                'description': event.description,
                'trigger_value': event.trigger_value,
                'threshold': event.threshold
            })
        
        return sorted(history, key=lambda x: x['timestamp'], reverse=True)
    
    def export_session_data(self, filename: Optional[str] = None) -> str:
        """导出会话数据"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"data/session_export_{timestamp}.json"
        
        session_data = {
            'metadata': {
                'export_time': time.time(),
                'session_stats': self.stats,
                'detector_stats': self.detector.get_statistics(),
                'ai_stats': self.ai_integrator.get_statistics()
            },
            'breakpoint_history': self.get_breakpoint_history(),
            'generated_units': self.ai_integrator.generated_units,
            'ai_responses': [
                {
                    'breakpoint_type': resp['breakpoint_event']['breakpoint_type'].value if hasattr(resp['breakpoint_event']['breakpoint_type'], 'value') else str(resp['breakpoint_event']['breakpoint_type']),
                    'organism_id': resp['breakpoint_event']['organism_id'],
                    'unit_design': resp['unit_design'],
                    'timestamp': resp['timestamp']
                }
                for resp in self.ai_integrator.ai_responses
            ]
        }
        
        import os
        import json
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        print(f"会话数据已导出到: {filename}")
        return filename
    
    def reset(self) -> None:
        """重置断点管理器"""
        self.is_paused = False
        self.pending_breakpoints.clear()
        self.processed_breakpoints.clear()
        
        # 重置统计
        self.stats = {
            'total_breakpoints': 0,
            'processed_breakpoints': 0,
            'successful_ai_calls': 0,
            'created_units': 0,
            'simulation_pauses': 0
        }
        
        print("断点管理器已重置")


class InteractiveBreakpointManager(BreakpointManager):
    """
    交互式断点管理器
    
    提供用户交互界面，允许手动控制断点处理流程。
    """
    
    def __init__(self):
        super().__init__()
        self.user_input_required = False
        self.current_event: Optional[BreakpointEvent] = None
    
    async def process_pending_breakpoints(self, organisms: List[Organism], grid: Grid) -> List[Dict[str, Any]]:
        """
        交互式处理断点
        """
        if not self.pending_breakpoints:
            return []
        
        new_unit_designs = []
        
        for event in self.pending_breakpoints:
            self.current_event = event
            
            # 显示断点信息
            self._display_breakpoint_info(event)
            
            # 等待用户决定
            user_choice = await self._get_user_choice()
            
            if user_choice == 'process':
                # 处理断点
                unit_design = await self.ai_integrator.process_breakpoint(event, organisms, grid)
                
                if unit_design:
                    new_unit_designs.append(unit_design)
                    self._display_unit_design(unit_design)
                    
                    # 询问是否应用
                    apply_choice = await self._get_apply_choice()
                    if apply_choice:
                        self.ai_integrator.save_unit_design(unit_design)
                        self.stats['created_units'] += 1
                
                self.processed_breakpoints.append(event)
                self.stats['processed_breakpoints'] += 1
                
            elif user_choice == 'skip':
                print("⏭️ 跳过此断点")
                self.processed_breakpoints.append(event)
                self.stats['processed_breakpoints'] += 1
            
            elif user_choice == 'pause':
                print("⏸️ 暂停处理")
                break
        
        # 清空已处理的断点
        for event in self.processed_breakpoints:
            if event in self.pending_breakpoints:
                self.pending_breakpoints.remove(event)
        
        self.current_event = None
        return new_unit_designs
    
    def _display_breakpoint_info(self, event: BreakpointEvent) -> None:
        """显示断点信息"""
        print(f"\n{'='*60}")
        print(f"🎯 断点触发!")
        print(f"类型: {event.breakpoint_type.value}")
        print(f"描述: {event.description}")
        print(f"触发值: {event.trigger_value}")
        print(f"阈值: {event.threshold}")
        print(f"生物体ID: {event.organism_id}")
        print(f"{'='*60}")
    
    async def _get_user_choice(self) -> str:
        """获取用户选择"""
        print("\n请选择操作:")
        print("1. [P]rocess - 处理此断点，调用AI生成新单位")
        print("2. [S]kip - 跳过此断点")
        print("3. [Pa]use - 暂停处理")
        
        # 在实际实现中，这里应该等待用户输入
        # 目前返回默认选择
        return 'process'
    
    async def _get_apply_choice(self) -> bool:
        """获取是否应用新单位的选择"""
        print("\n是否将新单位添加到游戏中?")
        print("1. [Y]es - 是")
        print("2. [N]o - 否")
        
        # 在实际实现中，这里应该等待用户输入
        # 目前返回默认选择
        return True
    
    def _display_unit_design(self, unit_design: Dict[str, Any]) -> None:
        """显示单位设计"""
        print(f"\n🤖 AI设计的新单位:")
        print(f"名称: {unit_design['name']} ({unit_design['english_name']})")
        print(f"功能: {unit_design['function']}")
        print(f"建造成本: {unit_design['build_cost']}")
        print(f"维持成本: {unit_design['upkeep_cost']}")
        print(f"机制: {unit_design['mechanism'][:100]}...")
        print(f"优势: {unit_design['evolutionary_advantages'][:100]}...")
