"""
网格系统 - 世界的空间结构

这个模块实现了游戏世界的网格系统，包括单元格管理、
边界处理、资源分布等核心功能。
"""

from typing import Optional, Tuple, List, Dict, Any
from enum import Enum
import numpy as np
from .base_unit import BaseUnit


class BoundaryType(Enum):
    """边界类型"""
    WALL = "wall"      # 硬边界
    WRAP = "wrap"      # 循环边界


class Cell:
    """
    网格单元格
    
    每个单元格可以包含一个单位、一定量的能量，或者为空。
    """
    
    def __init__(self, x: int, y: int):
        self.x = x
        self.y = y
        self.unit: Optional[BaseUnit] = None
        self.energy: float = 0.0
        self.max_energy: float = 100.0
        
    @property
    def position(self) -> Tuple[int, int]:
        """获取位置坐标"""
        return (self.x, self.y)
    
    @property
    def is_empty(self) -> bool:
        """检查是否为空"""
        return self.unit is None
    
    @property
    def has_energy(self) -> bool:
        """检查是否有能量"""
        return self.energy > 0
    
    def place_unit(self, unit: BaseUnit) -> bool:
        """
        放置单位
        
        Args:
            unit: 要放置的单位
            
        Returns:
            是否成功放置
        """
        if not self.is_empty:
            return False
        
        self.unit = unit
        unit.position = (self.x, self.y)
        return True
    
    def remove_unit(self) -> Optional[BaseUnit]:
        """
        移除单位
        
        Returns:
            被移除的单位，如果没有单位则返回None
        """
        unit = self.unit
        self.unit = None
        return unit
    
    def add_energy(self, amount: float) -> float:
        """
        添加能量
        
        Args:
            amount: 要添加的能量量
            
        Returns:
            实际添加的能量量
        """
        available_space = self.max_energy - self.energy
        added = min(amount, available_space)
        self.energy += added
        return added
    
    def consume_energy(self, amount: float) -> float:
        """
        消耗能量
        
        Args:
            amount: 要消耗的能量量
            
        Returns:
            实际消耗的能量量
        """
        consumed = min(amount, self.energy)
        self.energy -= consumed
        return consumed
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'position': (self.x, self.y),
            'unit': self.unit.to_dict() if self.unit else None,
            'energy': self.energy,
            'max_energy': self.max_energy
        }


class Grid:
    """
    游戏世界网格
    
    管理整个游戏世界的空间结构，包括单元格、边界处理、
    资源分布等功能。
    """
    
    def __init__(self, width: int, height: int, boundary_type: BoundaryType = BoundaryType.WRAP):
        self.width = width
        self.height = height
        self.boundary_type = boundary_type
        
        # 使用NumPy数组存储单元格，提高性能
        self.cells = np.empty((height, width), dtype=object)
        
        # 初始化所有单元格
        for y in range(height):
            for x in range(width):
                self.cells[y, x] = Cell(x, y)
        
        # 能量分布参数
        self.energy_generation_rate = 0.1
        self.energy_decay_rate = 0.01
        
    def is_valid_position(self, x: int, y: int) -> bool:
        """
        检查位置是否有效
        
        Args:
            x, y: 坐标
            
        Returns:
            位置是否在网格范围内
        """
        return 0 <= x < self.width and 0 <= y < self.height
    
    def normalize_position(self, x: int, y: int) -> Tuple[int, int]:
        """
        根据边界类型标准化位置
        
        Args:
            x, y: 原始坐标
            
        Returns:
            标准化后的坐标
        """
        if self.boundary_type == BoundaryType.WRAP:
            # 循环边界
            x = x % self.width
            y = y % self.height
            return (x, y)
        else:
            # 硬边界
            x = max(0, min(x, self.width - 1))
            y = max(0, min(y, self.height - 1))
            return (x, y)
    
    def get_cell(self, x: int, y: int) -> Optional[Cell]:
        """
        获取指定位置的单元格
        
        Args:
            x, y: 坐标
            
        Returns:
            单元格对象，如果位置无效则返回None
        """
        if self.boundary_type == BoundaryType.WRAP:
            x, y = self.normalize_position(x, y)
        elif not self.is_valid_position(x, y):
            return None
        
        return self.cells[y, x]
    
    def get_neighbors(self, x: int, y: int, radius: int = 1) -> List[Cell]:
        """
        获取指定位置周围的邻居单元格
        
        Args:
            x, y: 中心坐标
            radius: 邻居半径
            
        Returns:
            邻居单元格列表
        """
        neighbors = []
        
        for dy in range(-radius, radius + 1):
            for dx in range(-radius, radius + 1):
                if dx == 0 and dy == 0:
                    continue  # 跳过中心位置
                
                neighbor_x, neighbor_y = x + dx, y + dy
                cell = self.get_cell(neighbor_x, neighbor_y)
                if cell:
                    neighbors.append(cell)
        
        return neighbors
    
    def place_unit(self, unit: BaseUnit, x: int, y: int) -> bool:
        """
        在指定位置放置单位
        
        Args:
            unit: 要放置的单位
            x, y: 目标坐标
            
        Returns:
            是否成功放置
        """
        cell = self.get_cell(x, y)
        if not cell:
            return False
        
        return cell.place_unit(unit)
    
    def move_unit(self, from_x: int, from_y: int, to_x: int, to_y: int) -> bool:
        """
        移动单位
        
        Args:
            from_x, from_y: 起始坐标
            to_x, to_y: 目标坐标
            
        Returns:
            是否成功移动
        """
        from_cell = self.get_cell(from_x, from_y)
        to_cell = self.get_cell(to_x, to_y)
        
        if not from_cell or not to_cell or from_cell.is_empty or not to_cell.is_empty:
            return False
        
        unit = from_cell.remove_unit()
        if unit and to_cell.place_unit(unit):
            return True
        else:
            # 如果移动失败，将单位放回原位置
            if unit:
                from_cell.place_unit(unit)
            return False
    
    def remove_unit(self, x: int, y: int) -> Optional[BaseUnit]:
        """
        移除指定位置的单位
        
        Args:
            x, y: 坐标
            
        Returns:
            被移除的单位
        """
        cell = self.get_cell(x, y)
        if not cell:
            return None
        
        return cell.remove_unit()
    
    def distribute_energy(self, total_energy: float) -> None:
        """
        在网格中分布能量
        
        Args:
            total_energy: 要分布的总能量
        """
        # 简单的随机分布策略
        cells_count = self.width * self.height
        energy_per_cell = total_energy / cells_count
        
        for y in range(self.height):
            for x in range(self.width):
                cell = self.cells[y, x]
                cell.add_energy(energy_per_cell)
    
    def update_energy(self, delta_time: float) -> None:
        """
        更新网格中的能量分布
        
        Args:
            delta_time: 时间增量
        """
        for y in range(self.height):
            for x in range(self.width):
                cell = self.cells[y, x]
                
                # 能量自然生成
                if cell.energy < cell.max_energy:
                    generated = self.energy_generation_rate * delta_time
                    cell.add_energy(generated)
                
                # 能量衰减
                if cell.energy > 0:
                    decayed = self.energy_decay_rate * delta_time
                    cell.consume_energy(decayed)
    
    def get_energy_map(self) -> np.ndarray:
        """
        获取能量分布图
        
        Returns:
            能量分布的NumPy数组
        """
        energy_map = np.zeros((self.height, self.width))
        
        for y in range(self.height):
            for x in range(self.width):
                energy_map[y, x] = self.cells[y, x].energy
        
        return energy_map
    
    def get_unit_map(self) -> np.ndarray:
        """
        获取单位分布图
        
        Returns:
            单位分布的NumPy数组（0=空，1=有单位）
        """
        unit_map = np.zeros((self.height, self.width), dtype=int)
        
        for y in range(self.height):
            for x in range(self.width):
                if not self.cells[y, x].is_empty:
                    unit_map[y, x] = 1
        
        return unit_map
    
    def find_empty_cells(self) -> List[Tuple[int, int]]:
        """
        查找所有空的单元格
        
        Returns:
            空单元格的坐标列表
        """
        empty_cells = []
        
        for y in range(self.height):
            for x in range(self.width):
                if self.cells[y, x].is_empty:
                    empty_cells.append((x, y))
        
        return empty_cells
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取网格统计信息
        
        Returns:
            统计信息字典
        """
        total_energy = 0.0
        unit_count = 0
        empty_count = 0
        
        for y in range(self.height):
            for x in range(self.width):
                cell = self.cells[y, x]
                total_energy += cell.energy
                
                if cell.is_empty:
                    empty_count += 1
                else:
                    unit_count += 1
        
        return {
            'total_energy': total_energy,
            'average_energy': total_energy / (self.width * self.height),
            'unit_count': unit_count,
            'empty_count': empty_count,
            'occupancy_rate': unit_count / (self.width * self.height)
        }
