"""
性能测试脚本

测试核心系统在不同规模下的性能表现，为Cython优化提供基准数据。
"""

import sys
import os
import time
import random
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.base_unit import UnitType, create_unit
from core.grid import Grid, BoundaryType
from core.organism import Organism
from utils.config import config


def create_test_organisms(grid: Grid, count: int) -> list:
    """创建测试用的生物体"""
    organisms = []
    
    for i in range(count):
        organism = Organism(f"perf_test_{i}")
        
        # 随机选择位置
        empty_cells = grid.find_empty_cells()
        if len(empty_cells) < 2:
            break
        
        start_x, start_y = random.choice(empty_cells)
        
        # 创建基本结构
        core = create_unit(UnitType.CORE, f"core_{i}", (start_x, start_y))
        absorber = create_unit(UnitType.ABSORBER, f"absorber_{i}", (start_x + 1, start_y))
        
        if (grid.place_unit(core, start_x, start_y) and 
            grid.place_unit(absorber, start_x + 1, start_y)):
            
            organism.add_unit(core)
            organism.add_unit(absorber)
            organism.energy = 200.0  # 给更多初始能量
            organisms.append(organism)
    
    return organisms


def benchmark_simulation(grid_size: int, organism_count: int, steps: int) -> dict:
    """基准测试模拟性能"""
    print(f"测试配置: 网格{grid_size}x{grid_size}, {organism_count}个生物体, {steps}步")
    
    # 创建网格
    grid = Grid(grid_size, grid_size, BoundaryType.WRAP)
    grid.distribute_energy(grid_size * grid_size * 10)
    
    # 创建生物体
    organisms = create_test_organisms(grid, organism_count)
    actual_count = len(organisms)
    
    print(f"实际创建了 {actual_count} 个生物体")
    
    # 开始计时
    start_time = time.time()
    
    # 运行模拟
    for step in range(steps):
        # 更新网格
        grid.update_energy(1.0)
        
        # 更新生物体
        alive_organisms = []
        for organism in organisms:
            organism.update(1.0, grid)
            if organism.is_alive:
                alive_organisms.append(organism)
        
        organisms = alive_organisms
        
        # 每100步输出一次进度
        if (step + 1) % 100 == 0:
            print(f"  步骤 {step + 1}/{steps}, 存活生物体: {len(organisms)}")
    
    # 结束计时
    end_time = time.time()
    total_time = end_time - start_time
    
    # 计算性能指标
    steps_per_second = steps / total_time
    organisms_per_second = actual_count * steps / total_time
    
    return {
        'grid_size': grid_size,
        'organism_count': actual_count,
        'steps': steps,
        'total_time': total_time,
        'steps_per_second': steps_per_second,
        'organisms_per_second': organisms_per_second,
        'final_organism_count': len(organisms)
    }


def run_performance_tests():
    """运行一系列性能测试"""
    print("元演化生命游戏 - 性能测试")
    print("=" * 60)
    
    test_configs = [
        # (网格大小, 生物体数量, 步数)
        (50, 10, 500),
        (100, 20, 500),
        (200, 50, 300),
        (300, 100, 200),
    ]
    
    results = []
    
    for grid_size, organism_count, steps in test_configs:
        print(f"\n{'='*60}")
        result = benchmark_simulation(grid_size, organism_count, steps)
        results.append(result)
        
        print(f"结果:")
        print(f"  总耗时: {result['total_time']:.2f} 秒")
        print(f"  模拟速度: {result['steps_per_second']:.2f} 步/秒")
        print(f"  生物体处理速度: {result['organisms_per_second']:.2f} 生物体·步/秒")
        print(f"  最终存活生物体: {result['final_organism_count']}")
    
    # 输出汇总
    print(f"\n{'='*60}")
    print("性能测试汇总:")
    print(f"{'网格大小':<10} {'生物体':<8} {'步数':<6} {'耗时(s)':<10} {'步/秒':<10} {'生物体·步/秒':<15}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['grid_size']}x{result['grid_size']:<6} "
              f"{result['organism_count']:<8} "
              f"{result['steps']:<6} "
              f"{result['total_time']:<10.2f} "
              f"{result['steps_per_second']:<10.2f} "
              f"{result['organisms_per_second']:<15.2f}")
    
    # 性能分析
    print(f"\n性能分析:")
    if results:
        avg_steps_per_sec = sum(r['steps_per_second'] for r in results) / len(results)
        avg_organisms_per_sec = sum(r['organisms_per_second'] for r in results) / len(results)
        
        print(f"  平均模拟速度: {avg_steps_per_sec:.2f} 步/秒")
        print(f"  平均生物体处理速度: {avg_organisms_per_sec:.2f} 生物体·步/秒")
        
        # 性能建议
        if avg_steps_per_sec < 50:
            print(f"  建议: 模拟速度较慢，考虑使用Cython优化核心循环")
        elif avg_steps_per_sec < 100:
            print(f"  建议: 性能中等，可以考虑优化算法或使用Cython")
        else:
            print(f"  建议: 性能良好，当前实现已经足够高效")


def memory_usage_test():
    """内存使用测试"""
    print(f"\n{'='*60}")
    print("内存使用测试:")
    
    import psutil
    import gc
    
    process = psutil.Process()
    
    # 基准内存使用
    gc.collect()
    baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"基准内存使用: {baseline_memory:.2f} MB")
    
    # 创建大规模模拟
    grid = Grid(500, 500, BoundaryType.WRAP)
    organisms = create_test_organisms(grid, 200)
    
    gc.collect()
    simulation_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = simulation_memory - baseline_memory
    
    print(f"模拟内存使用: {simulation_memory:.2f} MB")
    print(f"内存增长: {memory_increase:.2f} MB")
    print(f"每个生物体平均内存: {memory_increase / len(organisms):.3f} MB")
    
    # 清理
    del grid
    del organisms
    gc.collect()
    
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"清理后内存: {final_memory:.2f} MB")


def main():
    """主函数"""
    try:
        run_performance_tests()
        memory_usage_test()
        
        print(f"\n{'='*60}")
        print("性能测试完成！")
        print("基于测试结果，可以确定是否需要进行Cython优化。")
        
    except Exception as e:
        print(f"性能测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
